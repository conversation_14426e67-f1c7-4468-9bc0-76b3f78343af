{layout name="layout" /}

<style>
.qualification-package {
    border: 1px solid #e6e6e6;
    border-radius: 8px;
    margin-bottom: 20px;
    background: #fff;
}

.package-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 15px 20px;
    border-bottom: 1px solid #e6e6e6;
    border-radius: 8px 8px 0 0;
}

.package-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.package-mode {
    font-size: 12px;
    color: #666;
    background: #e3f2fd;
    padding: 2px 8px;
    border-radius: 12px;
    display: inline-block;
}

.qualification-list {
    padding: 20px;
}

.qualification-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 2px solid #f0f0f0;
    border-radius: 8px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.qualification-item:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.qualification-item.uploaded {
    border-color: #52c41a;
    background: #f6ffed;
}

.qualification-item.rejected {
    border-color: #ff4d4f;
    background: #fff2f0;
}

.qualification-info {
    flex: 1;
    margin-right: 15px;
}

.qualification-name {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.qualification-desc {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
}

.qualification-meta {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-badge {
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.status-pending {
    background: #fff7e6;
    color: #fa8c16;
}

.status-approved {
    background: #f6ffed;
    color: #52c41a;
}

.status-rejected {
    background: #fff2f0;
    color: #ff4d4f;
}

.status-not-uploaded {
    background: #f5f5f5;
    color: #999;
}

.qualification-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-upload {
    background: #1890ff;
    color: white;
}

.btn-upload:hover {
    background: #40a9ff;
}

.btn-view {
    background: #52c41a;
    color: white;
}

.btn-view:hover {
    background: #73d13d;
}

.btn-reupload {
    background: #fa8c16;
    color: white;
}

.btn-reupload:hover {
    background: #ffa940;
}

.goods-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #999;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 15px;
    color: #d9d9d9;
}

.loading-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}
</style>

<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>商品资质管理</h3>
        </div>
        
        <div class="layui-card-body">
            <!-- 商品信息 -->
            <div class="goods-info">
                <div style="display: flex; align-items: center; gap: 15px;">
                    <img src="{$goods.image|default='/static/common/image/default_goods.png'}" 
                         style="width: 60px; height: 60px; border-radius: 6px; object-fit: cover;">
                    <div>
                        <div style="font-size: 16px; font-weight: 600; color: #333; margin-bottom: 5px;">
                            {$goods.name}
                        </div>
                        <div style="font-size: 12px; color: #666;">
                            商品ID: {$goods.id} | 
                            状态: {if $goods.status == 1}<span style="color: #52c41a;">上架</span>{else}<span style="color: #ff4d4f;">下架</span>{/if}
                        </div>
                    </div>
                </div>
            </div>

            <!-- 资质包列表 -->
            <div id="qualification-packages">
                <div class="loading-state">
                    <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>
                    <div>正在加载资质信息...</div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
layui.use(['layer'], function() {
    var layer = layui.layer;
    
    // 页面加载时获取资质信息
    loadQualifications();
    
    /**
     * 加载商品资质信息
     */
    function loadQualifications() {
        $.ajax({
            url: '{:url("getRequiredQualifications")}',
            type: 'GET',
            data: { goods_id: {$goods_id} },
            success: function(res) {
                if (res.code === 1) {
                    renderQualifications(res.data);
                } else {
                    showError(res.msg || '加载失败');
                }
            },
            error: function() {
                showError('网络错误，请稍后重试');
            }
        });
    }
    
    /**
     * 渲染资质列表
     */
    function renderQualifications(packages) {
        var html = '';
        
        if (!packages || packages.length === 0) {
            html = `
                <div class="empty-state">
                    <i class="layui-icon layui-icon-file"></i>
                    <div>该商品暂无资质要求</div>
                    <div style="font-size: 12px; color: #999; margin-top: 8px;">
                        请联系管理员为商品分类或商品设置资质包
                    </div>
                </div>
            `;
        } else {
            packages.forEach(function(package) {
                html += `
                    <div class="qualification-package">
                        <div class="package-header">
                            <div class="package-title">${package.package_name}</div>
                            <div class="package-mode">
                                ${package.selection_mode_text}
                                ${package.remark ? ' - ' + package.remark : ''}
                            </div>
                        </div>
                        <div class="qualification-list">
                `;
                
                if (package.qualifications && package.qualifications.length > 0) {
                    package.qualifications.forEach(function(qual) {
                        var statusClass = getStatusClass(qual.status);
                        var statusText = getStatusText(qual.status);
                        var itemClass = qual.status === 1 ? 'uploaded' : (qual.status === 2 ? 'rejected' : '');
                        
                        html += `
                            <div class="qualification-item ${itemClass}">
                                <div class="qualification-info">
                                    <div class="qualification-name">${qual.name}</div>
                                    <div class="qualification-desc">${qual.description}</div>
                                    <div class="qualification-meta">
                                        <span class="status-badge ${statusClass}">${statusText}</span>
                                        <span style="font-size: 11px; color: #999;">
                                            有效期: ${qual.valid_days_text}
                                        </span>
                                        ${qual.is_goods_specific ? '<span style="font-size: 11px; color: #1890ff;">商品专用</span>' : '<span style="font-size: 11px; color: #666;">通用资质</span>'}
                                    </div>
                                </div>
                                <div class="qualification-actions">
                                    ${generateActionButtons(qual)}
                                </div>
                            </div>
                        `;
                    });
                } else {
                    html += '<div style="color: #999; text-align: center; padding: 20px;">该资质包暂无资质项目</div>';
                }
                
                html += `
                        </div>
                    </div>
                `;
            });
        }
        
        $('#qualification-packages').html(html);
    }
    
    /**
     * 获取状态样式类
     */
    function getStatusClass(status) {
        switch(status) {
            case 0: return 'status-not-uploaded';
            case 1: return 'status-approved';
            case 2: return 'status-rejected';
            default: return 'status-pending';
        }
    }
    
    /**
     * 获取状态文本
     */
    function getStatusText(status) {
        switch(status) {
            case 0: return '未上传';
            case 1: return '审核通过';
            case 2: return '审核拒绝';
            default: return '待审核';
        }
    }
    
    /**
     * 生成操作按钮
     */
    function generateActionButtons(qual) {
        var buttons = '';
        
        if (qual.status === 0) {
            // 未上传
            buttons = `<button class="action-btn btn-upload" onclick="uploadQualification(${qual.id}, '${qual.name}')">上传资质</button>`;
        } else if (qual.status === 1) {
            // 审核通过
            buttons = `
                <button class="action-btn btn-view" onclick="viewQualification(${qual.shop_qualification_id})">查看资质</button>
                <button class="action-btn btn-reupload" onclick="uploadQualification(${qual.id}, '${qual.name}')">重新上传</button>
            `;
        } else if (qual.status === 2) {
            // 审核拒绝
            buttons = `
                <button class="action-btn btn-view" onclick="viewQualification(${qual.shop_qualification_id})">查看资质</button>
                <button class="action-btn btn-reupload" onclick="uploadQualification(${qual.id}, '${qual.name}')">重新上传</button>
            `;
        }
        
        return buttons;
    }
    
    /**
     * 上传资质
     */
    window.uploadQualification = function(qualificationId, qualificationName) {
        var url = '{:url("upload")}?goods_id={$goods_id}&qualification_id=' + qualificationId + '&qualification_name=' + encodeURIComponent(qualificationName);
        
        layer.open({
            type: 2,
            title: '上传资质 - ' + qualificationName,
            content: url,
            area: ['80%', '90%'],
            maxmin: true,
            end: function() {
                // 关闭弹窗后重新加载资质信息
                loadQualifications();
            }
        });
    };
    
    /**
     * 查看资质
     */
    window.viewQualification = function(shopQualificationId) {
        // 这里可以实现查看资质详情的功能
        layer.msg('查看资质功能开发中...', {icon: 0});
    };
    
    /**
     * 显示错误信息
     */
    function showError(message) {
        $('#qualification-packages').html(`
            <div class="empty-state">
                <i class="layui-icon layui-icon-close" style="color: #ff4d4f;"></i>
                <div style="color: #ff4d4f;">${message}</div>
                <div style="margin-top: 15px;">
                    <button class="layui-btn layui-btn-sm" onclick="loadQualifications()">重新加载</button>
                </div>
            </div>
        `);
    }
});
</script>
