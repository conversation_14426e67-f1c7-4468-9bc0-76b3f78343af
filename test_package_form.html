<!DOCTYPE html>
<html>
<head>
    <title>测试资质包表单</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        button { margin: 5px; padding: 8px 15px; }
        iframe { width: 100%; height: 600px; border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>资质包表单测试</h1>
    
    <div class="test-section">
        <h2>添加资质包表单</h2>
        <button onclick="loadAddForm()">加载添加表单</button>
        <div id="add-form-container"></div>
    </div>
    
    <div class="test-section">
        <h2>编辑资质包表单</h2>
        <button onclick="loadEditForm()">加载编辑表单 (ID=1)</button>
        <div id="edit-form-container"></div>
    </div>

    <div class="test-section">
        <h2>测试数据接口</h2>
        <button onclick="testQualifications()">测试获取资质数据</button>
        <div id="qualification-result" class="result"></div>
    </div>

    <script>
        function loadAddForm() {
            $('#add-form-container').html('<iframe src="/admin/goods.package/add"></iframe>');
        }

        function loadEditForm() {
            $('#edit-form-container').html('<iframe src="/admin/goods.package/edit?id=1"></iframe>');
        }

        function testQualifications() {
            $.ajax({
                url: '/admin/goods.qualification/lists',
                type: 'GET',
                data: { page: 1, limit: 10 },
                success: function(res) {
                    console.log('资质数据响应:', res);
                    $('#qualification-result').html('<h3>资质数据:</h3><pre>' + JSON.stringify(res, null, 2) + '</pre>');
                },
                error: function(xhr, status, error) {
                    console.error('获取资质数据错误:', error);
                    $('#qualification-result').html('<h3>错误:</h3>' + error);
                }
            });
        }
    </script>
</body>
</html>