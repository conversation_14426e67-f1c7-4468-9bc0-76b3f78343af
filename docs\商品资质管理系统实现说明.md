# 商品资质管理系统实现说明

## 概述

本系统实现了完整的商品资质管理功能，支持商品独立资质设置和分类资质设置，包含资质包管理、选择模式控制等功能。

## 核心功能

### 1. 商品资质设置模式
- **分类资质模式**：商品继承分类的资质要求
- **独立资质模式**：商品使用独立的资质设置（`use_custom_qualification = 1`）

### 2. 资质包选择模式
- **全部需要（selection_mode = 1）**：资质包内所有资质都必须上传并审核通过
- **任选其一（selection_mode = 2）**：资质包内至少有一个资质审核通过即可

### 3. 资质类型
- **通用资质（goods_id = 0）**：适用于商家所有商品
- **商品专用资质（goods_id > 0）**：仅适用于指定商品

## 数据库变更

### shop_qualification 表结构调整
```sql
-- 添加商品ID字段
ALTER TABLE `ls_shop_qualification` 
ADD COLUMN `goods_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '商品ID，0表示通用资质，非0表示商品专用资质' 
AFTER `qualification_name`;

-- 添加索引
ALTER TABLE `ls_shop_qualification` 
ADD INDEX `idx_goods_id` (`goods_id`);

-- 修改唯一索引
ALTER TABLE `ls_shop_qualification` 
DROP INDEX `uk_shop_qualification`;

ALTER TABLE `ls_shop_qualification` 
ADD UNIQUE KEY `uk_shop_qualification_goods` (`shop_id`, `qualification_id`, `goods_id`, `del`);
```

## 核心逻辑实现

### 1. Admin模块 - 资质审核获取逻辑

**文件**: `app/admin/logic/goods/GoodsLogic.php`

**核心方法**:
- `getQualificationAudit()`: 主入口方法，根据商品设置选择获取方式
- `getGoodsCustomQualificationAudit()`: 获取商品独立资质
- `getCategoryQualificationAudit()`: 获取分类资质
- `buildQualificationAuditData()`: 构建资质审核数据

**逻辑流程**:
1. 检查商品的 `use_custom_qualification` 字段
2. 如果为1，使用商品独立资质设置
3. 如果为0，使用分类资质设置
4. 查询资质包和包含的资质
5. 查询商家已上传的资质状态（优先商品专用，其次通用）
6. 构建完整的资质审核数据

### 2. Shop模块 - 商品资质管理

**文件**: `app/shop/logic/ShopQualificationLogic.php`

**新增方法**:
- `getGoodsRequiredQualifications()`: 获取商品需要的资质列表
- `checkGoodsQualificationComplete()`: 检查商品资质是否完整

**修改方法**:
- `upload()`: 支持商品ID参数，区分通用资质和商品专用资质

### 3. 控制器和视图

**新增文件**:
- `app/shop/controller/GoodsQualification.php`: 商品资质管理控制器
- `app/shop/view/goods_qualification/index.html`: 商品资质列表页面
- `app/shop/view/goods_qualification/upload.html`: 资质上传页面

## 使用流程

### 管理员端
1. 创建资质包，设置选择模式
2. 为分类或商品关联资质包
3. 审核商家上传的资质

### 商家端
1. 查看商品需要的资质要求
2. 根据资质包的选择模式上传相应资质
3. 等待管理员审核

## API接口

### 获取商品资质要求
```
GET /admin/goods.goods/getQualificationAudit.html
参数: category_id, goods_id
```

### 获取商品需要的资质列表
```
GET /shop/goods_qualification/getRequiredQualifications
参数: goods_id
```

### 上传商品资质
```
POST /shop/goods_qualification/upload
参数: goods_id, qualification_id, document_path, document_name, expire_time
```

### 检查资质完整性
```
GET /shop/goods_qualification/checkComplete
参数: goods_id
```

## 资质查询优先级

当查询商品资质时，系统按以下优先级查找：
1. 商品专用资质（goods_id = 商品ID）
2. 通用资质（goods_id = 0）

这样设计的好处：
- 商家可以为特定商品上传专用资质
- 也可以上传通用资质供多个商品使用
- 专用资质优先级更高，提供更精确的控制

## 资质包选择模式说明

### 全部需要模式（selection_mode = 1）
- 资质包内所有资质都必须上传
- 所有资质都必须审核通过
- 适用于严格的资质要求场景

### 任选其一模式（selection_mode = 2）
- 资质包内至少上传一个资质
- 至少有一个资质审核通过即可
- 适用于多选一的资质要求场景

## 注意事项

1. **数据迁移**: 执行SQL脚本前请备份数据库
2. **权限控制**: 确保商家只能操作自己的商品资质
3. **文件上传**: 注意文件大小和格式限制
4. **缓存更新**: 资质状态变更后需要更新相关缓存
5. **日志记录**: 重要操作需要记录操作日志

## 扩展功能建议

1. **资质到期提醒**: 定时检查资质有效期，提前提醒续期
2. **批量操作**: 支持批量上传、批量审核
3. **资质模板**: 为常见行业提供资质模板
4. **统计报表**: 资质审核统计、通过率分析
5. **移动端适配**: 优化移动端资质上传体验
