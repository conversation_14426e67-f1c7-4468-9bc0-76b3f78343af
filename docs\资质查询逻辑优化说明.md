# 资质查询逻辑优化说明

## 优化背景

根据您的反馈，`getGoodsCustomQualificationData` 和 `getCategoryQualificationData` 两个方法在获取需要的资质包和资质方面逻辑相似，唯一的区别是查询商家已上传资质时的 `goods_id` 参数不同。

## 核心差异分析

### 商品独立资质模式 (`use_custom_qualification = 1`)
- **需要的资质来源**：商品关联的资质包
- **商家上传资质查询**：`ls_shop_qualification` 中 `goods_id = 当前商品ID`
- **资质类型标识**：商品专用资质

### 分类资质模式 (`use_custom_qualification = 0`)
- **需要的资质来源**：分类关联的资质包 + 分类直接关联的独立资质
- **商家上传资质查询**：`ls_shop_qualification` 中 `goods_id = 0`（通用资质）
- **资质类型标识**：通用资质

## 优化方案

### 1. 统一资质包处理逻辑

创建了 `buildQualificationDataFromPackages()` 方法来统一处理资质包数据构建：

```php
private static function buildQualificationDataFromPackages($packageIds, $shopId, $queryGoodsId, $categoryId = null)
```

**参数说明**：
- `$packageIds`: 资质包ID数组
- `$shopId`: 商家ID
- `$queryGoodsId`: 查询商家资质时使用的goods_id（0=通用资质，>0=商品专用资质）
- `$categoryId`: 分类ID（仅分类模式需要，用于处理独立资质）

### 2. 简化调用逻辑

**商品独立资质模式**：
```php
private static function getGoodsCustomQualificationData($goodsId, $shopId)
{
    $packageIds = \app\common\model\goods\GoodsQualificationOverride::where('goods_id', $goodsId)
        ->column('package_id');
        
    // 使用商品ID查询商家上传的资质（商品专用资质）
    return self::buildQualificationDataFromPackages($packageIds, $shopId, $goodsId);
}
```

**分类资质模式**：
```php
private static function getCategoryQualificationData($categoryId, $goodsId, $shopId)
{
    $packageIds = \think\facade\Db::table('ls_qualification_package_category')
        ->where('category_id', $categoryId)
        ->column('package_id');
        
    // 使用goods_id=0查询商家上传的资质（通用资质）
    return self::buildQualificationDataFromPackages($packageIds, $shopId, 0, $categoryId);
}
```

### 3. 优化查询逻辑

**修改前**（复杂的查询逻辑）：
```php
$shopQualifications = \app\common\model\shop\ShopQualification::where([
    ['shop_id', '=', $shopId],
    ['qualification_id', 'in', $qualificationIds],
    ['del', '=', 0]
])
->where(function($query) use ($goodsId) {
    $query->where('goods_id', $goodsId)->whereOr('goods_id', 0);
})
->order('goods_id', 'desc') // 商品专用资质优先
->select()
->column('*', 'qualification_id');
```

**修改后**（精确查询）：
```php
$shopQualifications = \app\common\model\shop\ShopQualification::where([
    ['shop_id', '=', $shopId],
    ['qualification_id', 'in', $qualificationIds],
    ['goods_id', '=', $queryGoodsId], // 精确匹配
    ['del', '=', 0]
])
->select()
->column('*', 'qualification_id');
```

### 4. 修正资质类型标识

**修改前**：
```php
$qualData['is_goods_specific'] = $shopQual['goods_id'] > 0;
```

**修改后**：
```php
$qualData['is_goods_specific'] = $queryGoodsId > 0;
```

这样确保了资质类型标识的准确性，不依赖于数据库中的实际值。

## 优化效果

### 1. 代码复用性提升
- 消除了重复代码
- 统一了资质包处理逻辑
- 便于维护和扩展

### 2. 查询性能优化
- 去除了复杂的OR查询条件
- 使用精确的等值查询
- 减少了不必要的排序操作

### 3. 逻辑清晰度提升
- 明确区分了两种模式的差异
- 参数命名更加语义化
- 代码结构更加清晰

### 4. 数据准确性提升
- 资质类型标识更加准确
- 避免了数据不一致的问题

## 关键改进点

1. **统一入口**：`buildQualificationDataFromPackages()` 作为统一的资质包处理入口
2. **精确查询**：使用 `$queryGoodsId` 精确查询对应类型的商家资质
3. **参数语义化**：`$queryGoodsId` 明确表示查询时使用的goods_id值
4. **逻辑分离**：独立资质处理仅在分类模式下执行

## 测试建议

1. **商品独立资质模式测试**：
   - 验证查询的是 `goods_id = 商品ID` 的资质
   - 验证 `is_goods_specific = true`

2. **分类资质模式测试**：
   - 验证查询的是 `goods_id = 0` 的资质
   - 验证 `is_goods_specific = false`
   - 验证独立资质的正确处理

3. **性能测试**：
   - 对比优化前后的查询性能
   - 验证查询结果的准确性

---

**优化完成时间**：2025-01-11
**优化内容**：代码重构、性能优化、逻辑简化
**影响范围**：资质审核数据获取逻辑
