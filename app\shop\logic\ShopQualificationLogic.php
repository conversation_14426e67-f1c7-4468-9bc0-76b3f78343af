<?php

namespace app\shop\logic;

use app\common\model\shop\ShopQualification;
use app\common\model\shop\ShopQualificationLog;
use app\common\model\goods\Qualification;
use app\common\model\goods\GoodsCategoryQualification;

/**
 * 商家资质逻辑层
 * Class ShopQualificationLogic
 * @package app\shop\logic
 */
class ShopQualificationLogic
{
    /**
     * 获取商家资质列表
     */
    public static function lists($shopId, $get = [])
    {
        $where = ['shop_id' => $shopId, 'del' => 0];
        
        // 搜索条件
        if (!empty($get['qualification_name'])) {
            $where[] = ['qualification_name', 'like', '%' . trim($get['qualification_name']) . '%'];
        }
        
        if (isset($get['status']) && $get['status'] !== '') {
            $where['status'] = intval($get['status']);
        }

        $result = ShopQualification::where($where)
            ->with(['qualification'])
            ->order('create_time', 'desc')
            ->paginate([
                'list_rows' => $get['limit'] ?? 15,
                'page' => $get['page'] ?? 1
            ]);

        $lists = $result->items();
        
        // 处理数据
        foreach ($lists as $item) {
            $item['status_text'] = $item['status_text'];
            $item['expire_time_text'] = $item['expire_time_text'];
            $item['is_expired'] = $item['is_expired'];
            $item['audit_time_text'] = $item['audit_time_text'];
            $item['create_time_text'] =$item['create_time'];

            // 处理图片URL
            if (!empty($item['document_path'])) {
                $item['document_path'] = \app\common\server\UrlServer::getFileUrl($item['document_path']);
            }
        }

        return ['count' => $result->total(), 'lists' => $lists];
    }

    /**
     * 获取资质详情
     */
    public static function detail($id, $shopId)
    {
        $detail = ShopQualification::where(['id' => $id, 'shop_id' => $shopId, 'del' => 0])
            ->with(['qualification'])
            ->find();
            
        if (!$detail) {
            return null;
        }
        
        $detail['status_text'] = $detail['status_text'];
        $detail['expire_time_text'] = $detail['expire_time_text'];
        $detail['is_expired'] = $detail['is_expired'];
        $detail['audit_time_text'] = $detail['audit_time_text'];
        $detail['create_time_text'] = date('Y-m-d H:i:s', $detail['create_time']);
        
        return $detail;
    }

    /**
     * 上传资质
     */
    public static function upload($shopId, $post)
    {
        $goodsId = isset($post['goods_id']) ? intval($post['goods_id']) : 0;

        // 检查是否已存在该资质（考虑商品ID）
        $exists = ShopQualification::where([
            'shop_id' => $shopId,
            'qualification_id' => $post['qualification_id'],
            'goods_id' => $goodsId,
            'del' => 0
        ])->find();

        if ($exists) {
            // 更新现有资质
            $updateData = [
                'document_path' => $post['document_path'],
                'document_name' => $post['document_name'],
                'status' => 0, // 重新提交，状态设置为待审核
                'update_time' => time()
            ];

            if (isset($post['expire_time']) && $post['expire_time']) {
                $updateData['expire_time'] = strtotime($post['expire_time']);
            }

            $result = $exists->save($updateData);

            // 记录日志
            ShopQualificationLog::addLog(
                $exists->id,
                $shopId,
                $post['qualification_id'],
                'update',
                $exists->getData('status'),
                0,
                $goodsId > 0 ? '商家上传商品专用资质' : '商家上传通用资质',
                $shopId,
                1
            );
            
            return $result;
        } else {
            // 创建新资质
            $qualification = Qualification::find($post['qualification_id']);
            if (!$qualification) {
                throw new \Exception('资质不存在');
            }
            
            $data = [
                'shop_id' => $shopId,
                'qualification_id' => $post['qualification_id'],
                'qualification_name' => $qualification->name,
                'goods_id' => $goodsId,
                'document_path' => $post['document_path'],
                'document_name' => $post['document_name'],
                'expire_time' => 0,
                'status' => 0, // 新上传资质设置为待审核
                'create_time' => time(),
                'update_time' => time()
            ];
            
            if (isset($post['expire_time']) && $post['expire_time']) {
                $data['expire_time'] = strtotime($post['expire_time']);
            }
            
            $shopQualification = ShopQualification::create($data);
            
            // 记录日志
            ShopQualificationLog::addLog(
                $shopQualification->id,
                $shopId,
                $post['qualification_id'],
                'upload',
                0,
                0,
                $goodsId > 0 ? '上传商品专用资质文档' : '上传通用资质文档',
                $shopId,
                1
            );
            
            return $shopQualification;
        }
    }

    /**
     * 删除资质
     */
    public static function delete($id, $shopId)
    {
        $shopQualification = ShopQualification::where(['id' => $id, 'shop_id' => $shopId, 'del' => 0])->find();
        if (!$shopQualification) {
            return false;
        }
        
        $result = $shopQualification->save(['del' => 1, 'update_time' => time()]);
        
        // 记录日志
        ShopQualificationLog::addLog(
            $id,
            $shopId,
            $shopQualification->qualification_id,
            'delete',
            $shopQualification->status,
            $shopQualification->status,
            '删除资质',
            $shopId,
            1
        );
        
        return $result;
    }

    /**
     * 检查分类所需资质
     */
    public static function checkCategoryQualifications($shopId, $categoryId)
    {
        // 获取分类所需的资质
        $requiredQualifications = GoodsCategoryQualification::alias('gcq')
            ->leftJoin('qualification q', 'gcq.qualification_id = q.id')
            ->where([
                'gcq.category_id' => $categoryId,
                'q.status' => 1,
                'q.del' => 0
            ])
            ->field('q.id,q.name,q.description,q.is_required,q.document_path,q.document_name,q.ex_img')
            ->select();

        if (empty($requiredQualifications)) {
            return ['required' => [], 'missing' => [], 'expired' => [], 'optional_missing' => []];
        }
        
        // 获取商家已上传的资质
        $shopQualifications = ShopQualification::where([
            'shop_id' => $shopId,
            'del' => 0
        ])->column('qualification_id,status,expire_time', 'qualification_id');

        $missing = [];
        $expired = [];
        $optionalMissing = [];

        foreach ($requiredQualifications as $qualification) {
            $qualificationId = $qualification['id'];
            $isRequired = $qualification['is_required'];

            if (!isset($shopQualifications[$qualificationId])) {
                // 未上传
                if ($isRequired) {
                    $missing[] = $qualification;
                } else {
                    $optionalMissing[] = $qualification;
                }
            } else {
                $shopQual = $shopQualifications[$qualificationId];

                // 检查是否审核拒绝（只有审核拒绝状态才阻止提交商品）
                if ($shopQual['status'] == 2) {
                    $auit_remark=ShopQualification::where(['qualification_id' => $qualificationId, 'shop_id' => $shopId])->order('id', 'desc')->value('audit_remark');
                    $qualificationWithReason = array_merge($qualification->toArray(), [
                        'reason' => '审核拒绝:'. $auit_remark
                    ]);

                    if ($isRequired) {
                        $missing[] = $qualificationWithReason;
                    } else {
                        $optionalMissing[] = $qualificationWithReason;
                    }
                } else {
                    // 检查是否过期
                    if ($shopQual['expire_time'] > 0 && time() > $shopQual['expire_time']) {
                        if ($isRequired) {
                            $expired[] = $qualification;
                        } else {
                            $optionalMissing[] = $qualification;
                        }
                    }
                }
            }
        }

        // 处理图片URL
        $processQualifications = function($qualifications) {
            foreach ($qualifications as &$qualification) {
                if (!empty($qualification['ex_img'])) {
                    $qualification['ex_img'] = \app\common\server\UrlServer::getFileUrl($qualification['ex_img']);
                }
            }
            return $qualifications;
        };

        return [
            'required' => $processQualifications($requiredQualifications->toArray()),
            'missing' => $processQualifications($missing),
            'expired' => $processQualifications($expired),
            'optional_missing' => $processQualifications($optionalMissing)
        ];
    }

    /**
     * 获取所有可用资质
     */
    public static function getAvailableQualifications()
    {
        return Qualification::where(['status' => 1, 'del' => 0])
            ->field('id,name,description,document_path,document_name,ex_img')
            ->order('sort', 'asc')
            ->select();
    }

    /**
     * 获取商品需要的资质列表
     */
    public static function getGoodsRequiredQualifications($shopId, $goodsId)
    {
        // 获取商品信息
        $goods = \app\common\model\goods\Goods::find($goodsId);
        if (!$goods) {
            return [];
        }

        $requiredQualifications = [];

        // 检查商品是否使用独立资质设置
        if ($goods->use_custom_qualification == 1) {
            // 使用商品独立资质设置
            $packageIds = \app\common\model\goods\GoodsQualificationOverride::where('goods_id', $goodsId)
                ->column('package_id');
        } else {
            // 使用分类资质设置
            $categoryId = $goods->third_cate_id ?: $goods->second_cate_id ?: $goods->first_cate_id;
            $packageIds = \app\common\model\goods\GoodsCategoryQualification::where('category_id', $categoryId)
                ->column('package_id');
        }

        if (!empty($packageIds)) {
            // 获取资质包信息
            $packages = \app\model\goods\QualificationPackage::whereIn('id', $packageIds)
                ->where(['status' => 1, 'del' => 0])
                ->with(['qualifications' => function($query) {
                    $query->where(['status' => 1, 'del' => 0]);
                }])
                ->select();

            foreach ($packages as $package) {
                $packageData = [
                    'package_id' => $package->id,
                    'package_name' => $package->name,
                    'selection_mode' => $package->selection_mode,
                    'selection_mode_text' => $package->selection_mode == 1 ? '全部需要' : '任选其一',
                    'remark' => $package->remark,
                    'qualifications' => []
                ];

                // 获取商家已上传的资质状态
                $qualificationIds = $package->qualifications->column('id');
                $shopQualifications = [];

                if (!empty($qualificationIds)) {
                    // 优先查找商品专用资质，如果没有则查找通用资质
                    $shopQualifications = ShopQualification::where([
                        ['shop_id', '=', $shopId],
                        ['qualification_id', 'in', $qualificationIds],
                        ['del', '=', 0]
                    ])
                    ->where(function($query) use ($goodsId) {
                        $query->where('goods_id', $goodsId)->whereOr('goods_id', 0);
                    })
                    ->order('goods_id', 'desc') // 商品专用资质优先
                    ->select()
                    ->column('*', 'qualification_id');
                }

                foreach ($package->qualifications as $qualification) {
                    $qualData = [
                        'id' => $qualification->id,
                        'name' => $qualification->name,
                        'description' => $qualification->description,
                        'valid_days' => $qualification->valid_days,
                        'valid_days_text' => $qualification->valid_days == 0 ? '永久有效' : $qualification->valid_days . '天',
                        'status' => 0, // 0-未上传，1-审核通过，2-审核拒绝
                        'shop_qualification_id' => 0,
                        'document_path' => '',
                        'document_name' => '',
                        'expire_time' => 0,
                        'is_goods_specific' => false
                    ];

                    if (isset($shopQualifications[$qualification->id])) {
                        $shopQual = $shopQualifications[$qualification->id];
                        $qualData['status'] = $shopQual['status'];
                        $qualData['shop_qualification_id'] = $shopQual['id'];
                        $qualData['document_path'] = $shopQual['document_path'];
                        $qualData['document_name'] = $shopQual['document_name'];
                        $qualData['expire_time'] = $shopQual['expire_time'];
                        $qualData['is_goods_specific'] = $shopQual['goods_id'] > 0;
                    }

                    $packageData['qualifications'][] = $qualData;
                }

                $requiredQualifications[] = $packageData;
            }
        }

        return $requiredQualifications;
    }

    /**
     * 检查商品资质是否完整
     */
    public static function checkGoodsQualificationComplete($shopId, $goodsId)
    {
        $requiredQualifications = self::getGoodsRequiredQualifications($shopId, $goodsId);

        foreach ($requiredQualifications as $package) {
            if ($package['selection_mode'] == 1) {
                // 全部需要模式：所有资质都必须审核通过
                foreach ($package['qualifications'] as $qual) {
                    if ($qual['status'] != 1) {
                        return false;
                    }
                }
            } else {
                // 任选其一模式：至少有一个资质审核通过
                $hasApproved = false;
                foreach ($package['qualifications'] as $qual) {
                    if ($qual['status'] == 1) {
                        $hasApproved = true;
                        break;
                    }
                }
                if (!$hasApproved) {
                    return false;
                }
            }
        }

        return true;
    }
}
