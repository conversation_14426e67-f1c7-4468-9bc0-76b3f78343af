<?php

namespace app\admin\logic\goods;


use app\common\basics\Logic;
use app\common\enum\GoodsEnum;
use app\common\model\goods\Goods;
use app\common\model\goods\GoodsColumn;
use app\common\model\goods\GoodsImage;
use app\common\model\goods\GoodsItem;
use app\common\model\goods\GoodsSpec;
use app\common\model\goods\GoodsSpecValue;
use app\common\model\goods\Supplier;
use app\common\server\UrlServer;
use think\facade\Db;
use app\common\model\seckill\SeckillGoods;


/**
 * 商品管理-逻辑
 * Class GoodsLogic
 * @package app\shop\logic\goods
 */
class GoodsLogic extends Logic
{
    /*
     * 商品统计
     */
    public static function statistics($get = [])
    {
        $where = [
            ['del', '<>', GoodsEnum::DEL_TRUE]
        ];

        // 商家名称搜索
        if (isset($get['shop_name']) && $get['shop_name'] != '') {
            $shopIds = \app\common\model\shop\Shop::where('name', 'like', '%' . $get['shop_name'] . '%')->column('id');
            if (!empty($shopIds)) {
                $where[] = ['shop_id', 'in', $shopIds];
            } else {
                // 如果没有找到匹配的商家，返回0
                return [
                    'sell' => 0,
                    'warehouse' => 0,
                    'recycle' => 0,
                    'audit_stay' => 0,
                    'audit_refuse' => 0,
                ];
            }
        }

        // 商品名称搜索
        if (isset($get['goods_name']) && $get['goods_name'] != '') {
            $where[] = ['name', 'like', '%' . $get['goods_name'] . '%'];
        }

        // 平台分类搜索
        if (!empty($get['platform_cate_id'])) {
            $where[] = ['first_cate_id|second_cate_id|third_cate_id', '=', $get['platform_cate_id']];
        }

        // 店铺分类搜索
        if (!empty($get['shop_cate_id'])) {
            $where[] = ['shop_cate_id', '=', $get['shop_cate_id']];
        }

        // 商品类型搜索
        if (isset($get['goods_type']) && $get['goods_type'] != '') {
            $where[] = ['type', '=', $get['goods_type']];
        }

        // 商品栏目搜索
        if (isset($get['goods_column_id']) && $get['goods_column_id'] != '') {
            $where[] = ['goods_label_top|goods_label', 'like', '%' . $get['goods_column_id'] . '%'];
        }

        return [
            // 销售中商品(含库存预警商品)
            // 销售状态：上架中；删除状态：正常； 审核状态： 审核通过
            'sell'      => Goods::where($where)
                ->where('del', GoodsEnum::DEL_NORMAL)
                ->where('status', GoodsEnum::STATUS_SHELVES)
                ->where('audit_status', GoodsEnum::AUDIT_STATUS_OK)
                ->count(),
            // 仓库中商品
            // 销售状态：仓库中；删除状态：正常； 审核状态： 审核通过
            'warehouse' => Goods::where($where)
                ->where('del', GoodsEnum::DEL_NORMAL)
                ->where('status', GoodsEnum::STATUS_SOLD_OUT)
                ->where('audit_status', GoodsEnum::AUDIT_STATUS_OK)
                ->count(),
            // 回收站商品
            // 销售状态：任意；删除状态：回收站； 审核状态： 审核通过
            'recycle'   => Goods::where($where)
                ->where('del', GoodsEnum::DEL_RECYCLE)
                ->where('audit_status', GoodsEnum::AUDIT_STATUS_OK)
                ->count(),
            // 待审核商品
            // 销售状态：任意；删除状态：排除已删除； 审核状态： 待审核
            'audit_stay' => Goods::where($where)
                ->where('del', '<>', GoodsEnum::DEL_TRUE)
                ->where('audit_status', GoodsEnum::AUDIT_STATUS_STAY)
                ->count(),
            // 审核未通过商品
            // 销售状态：任意；删除状态：排除已删除； 审核状态： 审核未通过
            'audit_refuse' => Goods::where($where)
                ->where('del', '<>', GoodsEnum::DEL_TRUE)
                ->where('audit_status', GoodsEnum::AUDIT_STATUS_REFUSE)
                ->count(),
        ];
    }

    /**
     * Notes: 列表
     * @param $get
     * <AUTHOR> 10:53)
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function lists($get)
    {
        $where = [];
        if (isset($get['shop_name']) && !($get['shop_name'] == '')) {
            $where[] = ['s.name', 'like', '%' . $get['shop_name'] . '%'];
        }

        if (isset($get['goods_name']) && !($get['goods_name'] == '')) {
            $where[] = ['g.name', 'like', '%' . $get['goods_name'] . '%'];
        }
        if (!empty($get['platform_cate_id'])) {
            $where[] = ['g.first_cate_id|g.second_cate_id|g.third_cate_id', '=', $get['platform_cate_id']];
        }

        if (isset($get['goods_type']) && $get['goods_type'] != '') {
            $where[] = ['g.type', '=', $get['goods_type']];
        }

        if (isset($get['goods_column_id']) && $get['goods_column_id'] != '') {
            $where[] = ['g.goods_label_top|g.goods_label', 'like', '%' . $get['goods_column_id'] . '%'];
        }

        $type = $get['type'] ?? 0;
        switch ($type) {
            case 1:     //销售中
                $where[] = ['g.status', '=', GoodsEnum::STATUS_SHELVES]; //上架
                $where[] = ['g.del', '=', GoodsEnum::DEL_NORMAL];
                $where[] = ['g.audit_status', '=', GoodsEnum::AUDIT_STATUS_OK]; //审核通过
                break;
            case 2:      //仓库中
                $where[] = ['g.status', '=', GoodsEnum::STATUS_SOLD_OUT]; //下架
                $where[] = ['g.del', '=', GoodsEnum::DEL_NORMAL];
                $where[] = ['g.audit_status', '=', GoodsEnum::AUDIT_STATUS_OK]; //审核通过
                break;
            case 3:     //回收站
                $where[] = ['g.del', '=', GoodsEnum::DEL_RECYCLE];
                $where[] = ['g.audit_status', '=', GoodsEnum::AUDIT_STATUS_OK]; //审核通过
                break;
            case 4:  //待审核
                $where[] = ['g.del', '<>', GoodsEnum::DEL_TRUE];
                $where[] = ['g.audit_status', '=', GoodsEnum::AUDIT_STATUS_STAY];
                break;
            case 5: //审核未通过
                $where[] = ['g.del', '<>', GoodsEnum::DEL_TRUE];
                $where[] = ['g.audit_status', '=', GoodsEnum::AUDIT_STATUS_REFUSE];
                break;
            default:
                $where[] = ['g.del', '=', GoodsEnum::DEL_NORMAL];
        }

        $model = Goods::alias('g')
            ->field('g.id, g.image, g.spec_type, g.name, g.min_price, g.max_price, g.sales_actual, g.stock, g.sort_weight, g.create_time, g.goods_label_top, g.goods_label, g.audit_status, g.audit_remark, g.status, g.del, g.is_hot, g.is_recommend, g.first_cate_id, g.second_cate_id, g.third_cate_id, g.shop_cate_id, s.id as shop_id, s.name as shop_name, s.logo as shop_logo, s.type as shop_type, sa.mobile, fc.name as first_cate_name, sc.name as second_cate_name, tc.name as third_cate_name, shc.name as shop_cate_name')
            ->leftJoin('Shop s', 's.id=g.shop_id')
            ->leftJoin('shop_apply sa', 's.name = sa.name')
            ->leftJoin('goods_category fc', 'fc.id=g.first_cate_id')
            ->leftJoin('goods_category sc', 'sc.id=g.second_cate_id')
            ->leftJoin('goods_category tc', 'tc.id=g.third_cate_id')
            ->leftJoin('shop_category shc', 'shc.id=g.shop_cate_id')
            ->where($where);

        $count = (clone $model)->count();

        if (isset($get['page']) && isset($get['limit'])) {
            $lists = $model->page($get['page'], $get['limit'])->order('g.create_time', 'desc')->group('g.id')->select();
        } else {
            $lists = $model->order('g.create_time', 'desc')->select();
        }
        foreach ($lists as &$item) {
            $item['price'] = $item['spec_type'] == 1 ? $item["min_price"] : $item["min_price"] . " ~ " . $item["max_price"];
            switch ($item['shop_type']) {
                case 1:
                    $item['shop_type_desc'] = '官方自营';
                    break;
                case 2:
                    $item['shop_type_desc'] = '入驻商家';
                    break;
            }
            $item['shop_logo'] = empty($item['shop_logo']) ? '' : UrlServer::getFileUrl($item['shop_logo']);

            // 处理顶部标签
            if (!empty($item['goods_label_top'])) {
                $columnArr = explode(',', $item['goods_label_top']);
                $columnStr = '';
                foreach ($columnArr as $cloumnId) {
                    $columnName = GoodsColumn::where('id', $cloumnId)->value('name');
                    if ($columnName) {
                        $columnStr = $columnStr . $columnName . ',';
                    }
                }
                $columnStr = substr($columnStr, 0, strlen($columnStr) - 1);
                $item['topLabelStr'] = $columnStr ?: '';
            } else {
                $item['topLabelStr'] = '';
            }

            // 处理普通标签
            if (!empty($item['goods_label'])) {
                $columnArr = explode(',', $item['goods_label']);
                $columnStr = '';
                foreach ($columnArr as $cloumnId) {
                    $columnName = GoodsColumn::where('id', $cloumnId)->value('name');
                    if ($columnName) {
                        $columnStr = $columnStr . $columnName . ',';
                    }
                }
                $columnStr = substr($columnStr, 0, strlen($columnStr) - 1);
                $item['labelStr'] = $columnStr ?: '';
            } else {
                $item['labelStr'] = '';
            }

            // 合并标签显示
            $item['columnStr'] = trim($item['topLabelStr'] . ',' . $item['labelStr'], ',');
        }
        if ($count) {
            $lists = $lists->toArray();
        } else {
            $lists = [];
        }
        return ['count' => $count, 'lists' => $lists];
    }

    /**
     * 获取商品信息
     * @param $goods_id
     * @return array
     */
    public static function info($goods_id)
    {
        // 商品主表
        $info['base'] = Goods::where(['id' => $goods_id])
            ->withAttr('abs_image', function ($value, $data) {
                return UrlServer::getFileUrl($data['image']);
            })
            ->withAttr('content', function ($value) {
                $preg = '/(<img .*?src=")[^https|^http](.*?)(".*?>)/is';
                $local_url = UrlServer::getFileUrl('/');
                return  preg_replace($preg, "\${1}$local_url\${2}\${3}", $value);
            })
            ->withAttr('poster', function ($value) {
                return empty($value) ? '' : UrlServer::getFileUrl($value);
            })
            ->withAttr('abs_video', function ($value, $data) {
                if ($data['video']) {
                    return UrlServer::getFileUrl($data['video']);
                }
                return '';
            })->append(['abs_image', 'abs_video'])->find();
        // 商品轮播图
        $info['base']['goods_image'] = GoodsImage::where(['goods_id' => $goods_id])
            ->withAttr('abs_image', function ($value, $data) {
                return UrlServer::getFileUrl($data['uri']);
            })
            ->append(['abs_image'])
            ->select()
            ->toArray();

        // 商品SKU
        $info['item'] = GoodsItem::where(['goods_id' => $goods_id])
            ->withAttr('abs_image', function ($value, $data) {
                return $data['image'] ? UrlServer::getFileUrl($data['image']) : '';
            })->append(['abs_image'])
            ->select();
        // 商品规格项
        $info['spec'] = GoodsSpec::where(['goods_id' => $goods_id])->select();
        // 商品规格值
        $spec_value = GoodsSpecValue::where(['goods_id' => $goods_id])->select();

        $data = [];
        foreach ($spec_value as $k => $v) {
            $data[$v['spec_id']][] = $v;
        }
        foreach ($info['spec'] as $k => $v) {
            $info['spec'][$k]['values'] = isset($data[$v['id']]) ? $data[$v['id']] : [];
        }
        return $info;
    }

    /**
     * 违规重审
     * @param $params
     */
    public static function reAudit($params)
    {
        Db::startTrans();
        try {
            // 更新商品信息
            $updateData = [
                'id' => $params['goods_id'],
                'audit_remark' => trim($params['reason']),
                'audit_status' => GoodsEnum::AUDIT_STATUS_REFUSE
            ];
            Goods::update($updateData);
            // 对应的秒杀商品同步更新为待审核
            SeckillGoods::where([
                'del' => 0,
                'goods_id' => $params['goods_id']
            ])->update([
                'review_status' => 0,
                'update_time' => time()
            ]);

            event('UpdateCollect', ['goods_id' => $params['goods_id']]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 商品设置
     */
    public static function setInfo($params)
    {
        $updateData = [
            'id' => $params['goods_id'],
            'sales_virtual' => $params['sales_virtual'],
            'clicks_virtual' => $params['clicks_virtual'],
            'sort_weight' => $params['sort_weight'],
            'goods_label_top' => isset($params['goods_label_top']) ? $params['goods_label_top'] : '',
            'goods_label' => isset($params['goods_label']) ? $params['goods_label'] : ''
        ];
        return Goods::update($updateData);
    }

    /**
     * 审核
     */
    public static function audit($params)
    {
        $id = $params['goods_id'];
        $data = [
            'audit_status' => $params['audit_status'],
            'audit_remark' => $params['audit_remark'],
        ];
        // 如果审核通过，则自动上架
        if ($params['audit_status'] == GoodsEnum::AUDIT_STATUS_OK) {
            $data['status'] = GoodsEnum::STATUS_SHELVES;
        }
        return (new Goods)->where('id', $id)->update($data);
    }


    /**
     * @notes 批量下架
     * @param $params
     * @return bool
     * <AUTHOR>
     * @date 2022/9/20 6:17 下午
     */
    public static function moreLower($params)
    {
        Db::startTrans();
        try {
            $ids = explode(',', $params['ids']);
            foreach ($ids as $id) {
                // 更新商品信息
                $updateData = [
                    'id' => $id,
                    'audit_remark' => trim($params['reason']),
                    'audit_status' => GoodsEnum::AUDIT_STATUS_REFUSE
                ];
                Goods::update($updateData);
                // 对应的秒杀商品同步更新为待审核
                SeckillGoods::where([
                    'del' => 0,
                    'goods_id' => $id
                ])->update([
                    'review_status' => 0,
                    'update_time' => time()
                ]);

                event('UpdateCollect', ['goods_id' => $id]);
            }

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }
    /**
     * 更新商品基本信息
     * @param $params
     * @return bool
     */
    public static function updateBasicInfo($params)
    {
      // 验证商品ID
            if (empty($params['goods_id'])) {
                throw new \Exception('商品ID不能为空');
            }

             $updateData['use_custom_qualification'] = $params['use_custom_qualification'];

            // 更新平台分类
            if (isset($params['first_cate_id']) && $params['first_cate_id'] !== '') {
                $updateData['first_cate_id'] = intval($params['first_cate_id']);
            }
            if (isset($params['second_cate_id']) && $params['second_cate_id'] !== '') {
                $updateData['second_cate_id'] = intval($params['second_cate_id']);
            }
            if (isset($params['third_cate_id']) && $params['third_cate_id'] !== '') {
                $updateData['third_cate_id'] = intval($params['third_cate_id']);
            }

        
            // 更新销售状态
            if (isset($params['status'])) {
                $updateData['status'] = $params['status'];
            }

        

         

           
            // 替换内容中图片地址
           $domain = UrlServer::getFileUrl('/');
           $updateData['content'] = str_replace($domain, '/', $params['content']);
       
            $updateData['update_time'] = time();
            $res=Db::name('goods')->where('id',$params['goods_id'])->update($updateData);
            return $res;
    }

    /**
     * @notes 批量审核
     * @param $params
     * @return bool
     * <AUTHOR>
     * @date 2022/9/20 6:36 下午
     */
    public static function moreAudit($params)
    {
        $ids = explode(',', $params['ids']);
        $updateData = [
            'audit_status' => $params['audit_status'],
            'audit_remark' => $params['audit_remark'],
            'update_time'  => time(),
        ];
        if ($params['audit_status'] == GoodsEnum::AUDIT_STATUS_OK) {
            $updateData['status'] = GoodsEnum::STATUS_SHELVES;
        }
        (new Goods)->whereIn('id', $ids)->update($updateData);

        return true;
    }

    /**
     * 获取商品资质审核信息
     */
    public static function getQualificationAudit($categoryId, $goodsId)
    {
        // 获取商品信息
        $goods = Goods::where('id', $goodsId)->find();
        
        if (!$goods) {
            return [];
        }

        $result = [];

        // 检查商品是否使用独立资质设置
        if ($goods->use_custom_qualification == 1) {
            // 使用商品独立资质设置 - 获取商品关联的资质包和分类关联的资质
            $result = self::getGoodsCustomQualificationData($goodsId, $goods->shop_id,$categoryId);
        } else {
            // 使用分类资质设置 - 获取分类关联的资质包和资质
            $result = self::getCategoryQualificationData($categoryId, $goodsId, $goods->shop_id);
        }

        return $result;
    }

    /**
     * 获取商品独立资质数据
     */
    private static function getGoodsCustomQualificationData($goodsId, $shopId,$categoryId)
    {
        // 获取商品关联的资质包
        $packageIds =\think\facade\Db::table('ls_qualification_package_category')
            ->where('category_id', $categoryId)
            ->column('package_id');

        // 使用商品ID查询商家上传的资质（商品专用资质）
        return self::buildQualificationDataFromPackages($packageIds, $shopId, $goodsId,$categoryId);
    }

    /**
     * 获取分类资质数据
     */
    private static function getCategoryQualificationData($categoryId, $goodsId, $shopId)
    {
        // 获取分类关联的资质包
        $packageIds = \think\facade\Db::table('ls_qualification_package_category')
            ->where('category_id', $categoryId)
            ->column('package_id');

        // 使用goods_id=0查询商家上传的资质（通用资质）
        return self::buildQualificationDataFromPackages($packageIds, $shopId, 0, $categoryId);
    }

    /**
     * 根据资质包构建资质数据
     */
    private static function buildQualificationDataFromPackages($packageIds, $shopId, $queryGoodsId, $categoryId = null)
    {
        $result = [];
       
        // 1. 处理资质包
        if (!empty($packageIds)) {
            $packages = \app\model\goods\QualificationPackage::whereIn('id', $packageIds)
                ->where(['status' => 1, 'del' => 0])
                ->with(['qualifications' => function($query) {
                    $query->where(['status' => 1, 'del' => 0]);
                }])
                ->select();
                
            $result = array_merge($result, self::buildQualificationAuditData($packages, $shopId, $queryGoodsId));
        }
  
        // 2. 处理分类直接关联的独立资质（仅分类资质模式需要）
        if ($categoryId != null) {
            $qualificationIds = \think\facade\Db::table('ls_goods_category_qualification')
                ->where('category_id', $categoryId)
                ->column('qualification_id');
         
            if (!empty($qualificationIds)) {
                // 排除已经在资质包中的资质
                $packageQualificationIds = [];
                if (!empty($result)) {
                    foreach ($result as $package) {
                        if (isset($package['qualifications'])) {
                            foreach ($package['qualifications'] as $qual) {
                                $packageQualificationIds[] = $qual['id'];
                            }
                        }
                    }
                }

                $independentQualificationIds = array_diff($qualificationIds, $packageQualificationIds);

                if (!empty($independentQualificationIds)) {
                    $independentQualifications = \app\common\model\goods\Qualification::whereIn('id', $independentQualificationIds)
                        ->where(['status' => 1, 'del' => 0])
                        ->select();

                    if (!$independentQualifications->isEmpty()) {
                        // 检查是否有必传资质
                        $hasRequired = false;
                        $hasOptional = false;
                        foreach ($independentQualifications as $qual) {
                            if ($qual->is_required == 1) {
                                $hasRequired = true;
                            } else {
                                $hasOptional = true;
                            }
                        }

                        // 根据资质类型确定选择模式描述
                        $selectionModeText = '';
                        $selectionModeDesc = '';
                        if ($hasRequired && $hasOptional) {
                            $selectionModeText = '部分必传';
                            $selectionModeDesc = '标记为必传的资质必须上传，其他为可选资质';
                        } elseif ($hasRequired && !$hasOptional) {
                            $selectionModeText = '全部必传';
                            $selectionModeDesc = '所有资质都必须上传并审核通过';
                        } else {
                            $selectionModeText = '全部可选';
                            $selectionModeDesc = '所有资质都是可选的，建议上传以提升可信度';
                        }

                        // 创建一个虚拟的独立资质包
                        $independentPackage = [
                            'package_id' => 0,
                            'package_name' => '独立资质',
                            'selection_mode' => 3, // 使用特殊值表示混合模式
                            'selection_mode_text' => $selectionModeText,
                            'selection_mode_desc' => $selectionModeDesc,
                            'remark' => '分类直接关联的独立资质',
                            'qualifications' => []
                        ];

                        $independentPackage['qualifications'] = self::buildQualificationData($independentQualifications, $shopId, $queryGoodsId);
                        $result[] = $independentPackage;
                    }
                }
            }
        }

        return $result;
    }

    /**
     * 构建单个资质数据
     */
    private static function buildQualificationData($qualifications, $shopId, $queryGoodsId)
    {
        $result = [];
        $qualificationIds = $qualifications->column('id');

        // 获取商家已上传的资质状态
        $shopQualifications = [];
        if (!empty($qualificationIds)) {
            $shopQualifications = \app\common\model\shop\ShopQualification::where([
                ['shop_id', '=', $shopId],
                ['qualification_id', 'in', $qualificationIds],
                ['goods_id', '=', $queryGoodsId],
                ['del', '=', 0]
            ])
            ->select()
            ->column('*', 'qualification_id');
        }

        foreach ($qualifications as $qualification) {
            $qualData = [
                'id' => $qualification->id,
                'name' => $qualification->name,
                'description' => $qualification->description,
                'valid_days' => $qualification->valid_days,
                'valid_days_text' => $qualification->valid_days == 0 ? '永久有效' : $qualification->valid_days . '天',
                'ex_img' => $qualification->ex_img ?? '', // 示例图片
                'is_required' => $qualification->is_required ?? 0, // 是否必传
                'shop_qualification_id' => 0,
                'status' => 0, // 0-待提交，1-审核通过，2-审核拒绝
                'status_text' => '待提交',
                'audit_remark' => '',
                'document_path' => '',
                'document_name' => '',
                'expire_time' => 0,
                'expire_time_text' => '',
                'is_goods_specific' => false // 是否为商品专用资质
            ];

            if (isset($shopQualifications[$qualification->id])) {
                $shopQual = $shopQualifications[$qualification->id];
                $qualData['shop_qualification_id'] = $shopQual['id'];
                $qualData['status'] = $shopQual['status'];
                $qualData['status_text'] = self::getStatusText($shopQual['status']);
                $qualData['audit_remark'] = $shopQual['audit_remark'];
                $qualData['document_path'] = $shopQual['document_path'];
                $qualData['document_name'] = $shopQual['document_name'];
                $qualData['expire_time'] = $shopQual['expire_time'];
                $qualData['expire_time_text'] = $shopQual['expire_time'] > 0 ? date('Y-m-d H:i:s', $shopQual['expire_time']) : '';
                $qualData['is_goods_specific'] = $queryGoodsId > 0;
            }

            $result[] = $qualData;
        }

        return $result;
    }



    /**
     * 构建资质审核数据
     */
    private static function buildQualificationAuditData($packages, $shopId, $queryGoodsId)
    {
        $result = [];

        foreach ($packages as $package) {
            $packageData = [
                'package_id' => $package->id,
                'package_name' => $package->name,
                'selection_mode' => $package->selection_mode,
                'selection_mode_text' => $package->selection_mode == 1 ? '全部需要' : '任选其一',
                'selection_mode_desc' => $package->selection_mode == 1 ? '此资质包内的所有资质都必须上传并审核通过' : '此资质包内任选一个资质上传并审核通过即可',
                'remark' => $package->remark,
                'qualifications' => []
            ];

            // 获取该资质包下所有资质的商家上传情况
            $qualificationIds = $package->qualifications->column('id');

            if (!empty($qualificationIds)) {
                // 根据queryGoodsId查询对应的商家资质
                // queryGoodsId = 0 表示查询通用资质，> 0 表示查询商品专用资质
                $shopQualifications = \app\common\model\shop\ShopQualification::where([
                    ['shop_id', '=', $shopId],
                    ['qualification_id', 'in', $qualificationIds],
                    ['goods_id', '=', $queryGoodsId],
                    ['del', '=', 0]
                ])
                ->select()
                ->column('*', 'qualification_id');

                foreach ($package->qualifications as $qualification) {
                    $qualData = [
                        'id' => $qualification->id,
                        'name' => $qualification->name,
                        'description' => $qualification->description,
                        'valid_days' => $qualification->valid_days,
                        'valid_days_text' => $qualification->valid_days == 0 ? '永久有效' : $qualification->valid_days . '天',
                        'ex_img' => $qualification->ex_img ?? '', // 示例图片
                        'is_required' => $qualification->is_required ?? 0, // 是否必传
                        'shop_qualification_id' => 0,
                        'status' => 0, // 0-待提交，1-审核通过，2-审核拒绝
                        'status_text' => '待提交',
                        'audit_remark' => '',
                        'document_path' => '',
                        'document_name' => '',
                        'expire_time' => 0,
                        'expire_time_text' => '',
                        'is_goods_specific' => false // 是否为商品专用资质
                    ];

                    if (isset($shopQualifications[$qualification->id])) {
                        $shopQual = $shopQualifications[$qualification->id];
                        $qualData['shop_qualification_id'] = $shopQual['id'];
                        $qualData['status'] = $shopQual['status'];
                        $qualData['status_text'] = self::getStatusText($shopQual['status']);
                        $qualData['audit_remark'] = $shopQual['audit_remark'];
                        $qualData['document_path'] = $shopQual['document_path'];
                        $qualData['document_name'] = $shopQual['document_name'];
                        $qualData['expire_time'] = $shopQual['expire_time'];
                        $qualData['expire_time_text'] = $shopQual['expire_time'] > 0 ? date('Y-m-d H:i:s', $shopQual['expire_time']) : '';
                        $qualData['is_goods_specific'] = $queryGoodsId > 0;
                    }

                    $packageData['qualifications'][] = $qualData;
                }
            }

            $result[] = $packageData;
        }

        return $result;
    }

    /**
     * 获取状态文本
     */
    private static function getStatusText($status)
    {
        switch ($status) {
            case 0:
                return '待审核';
            case 1:
                return '审核通过';
            case 2:
                return '审核拒绝';
            default:
                return '未知状态';
        }
    }


    /**
     * 审核商家资质
     */
    public static function auditQualification($qualificationId, $auditStatus, $auditRemark = '')
    {
        $shopQualification = \app\common\model\shop\ShopQualification::find($qualificationId);
        if (!$shopQualification) {
            self::$error = '未找到要审核的资质记录';
            return false;
        }

        $shopQualification->audit_status = $auditStatus;
        $shopQualification->audit_remark = $auditRemark;
        $shopQualification->audit_time = time();
        return $shopQualification->save();
    }
}
