<?php

namespace app\common\server;

use GuzzleHttp\Client;
use think\facade\Cache;
use think\facade\Log;
use think\facade\Db;

class WechatService
{
    protected $appId;
    protected $appSecret;
    protected $client;
    protected static $error;

    const API_BASE = 'https://api.weixin.qq.com';

    public function __construct()
    {
        // 从配置服务获取小程序AppID和AppSecret
        $this->appId = ConfigServer::get('mnp', 'app_id');
        $this->appSecret = ConfigServer::get('mnp', 'secret');

        if (!$this->appId || !$this->appSecret) {
            throw new \Exception('微信小程序AppID或AppSecret未配置');
        }

        $this->client = new Client([
            'base_uri' => self::API_BASE,
            'timeout'  => 5.0,
        ]);
    }

    /**
     * @notes 身份证OCR识别
     * @param string $imageUrl 图片URL或本地路径
     * @param string $type 识别类型 Front/Back
     * @return array
     * <AUTHOR>
     * @date 2024/12/28
     */
    public static function idCardOcr($imageUrl, $type = 'Front')
    {
        try {
            $instance = new self();
            $accessToken = $instance->getAccessToken();
            if (!$accessToken) {
                return [
                    'errcode' => -1,
                    'errmsg' => '获取access_token失败'
                ];
            }

            // 构建请求URL
            $url = self::API_BASE . '/cv/ocr/idcard?access_token=' . $accessToken;

            // 判断是使用图片URL还是上传文件
            if (filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                // 使用图片URL，通过POST数据传递
                $postData = [
                    'img_url' => $imageUrl,
                    'type' => 'photo'  // 微信OCR接口固定使用photo类型
                ];
            } else {
                // 使用文件上传
                if (!file_exists($imageUrl)) {
                    return [
                        'errcode' => 101002,
                        'errmsg' => '图片文件不存在'
                    ];
                }

                // 检查文件大小（限制2MB）
                if (filesize($imageUrl) > 2 * 1024 * 1024) {
                    return [
                        'errcode' => 101002,
                        'errmsg' => '图片大小超过限制'
                    ];
                }

                $postData = [
                    'img' => new \CURLFile($imageUrl),
                    'type' => 'photo'  // 微信OCR接口固定使用photo类型
                ];
            }

            // 发送请求
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

            // 设置必要的请求头
            $headers = [
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept: application/json, text/plain, */*',
                'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
                'Cache-Control: no-cache',
            ];

            if (!empty($postData)) {
                // 如果是文件上传，让curl自动设置Content-Type
                if (isset($postData['img'])) {
                    // 文件上传时不设置Content-Type，让curl自动处理multipart/form-data
                    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
                } else {
                    // 使用图片URL时，编码为表单数据
                    $headers[] = 'Content-Type: application/x-www-form-urlencoded';
                    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
                }
            }

            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                Log::error('身份证OCR请求失败: ' . $error);
                return [
                    'errcode' => -1,
                    'errmsg' => '网络请求失败: ' . $error
                ];
            }

            if ($httpCode !== 200) {
                Log::error('身份证OCR请求失败，状态码: ' . $httpCode . '，响应内容: ' . $response);
                return [
                    'errcode' => -1,
                    'errmsg' => 'HTTP请求失败，状态码: ' . $httpCode . '，响应: ' . substr($response, 0, 200)
                ];
            }

            $result = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('身份证OCR响应解析失败: ' . $response);
                return [
                    'errcode' => -1,
                    'errmsg' => '响应数据解析失败'
                ];
            }

            // 在结果中添加识别类型信息
            if (isset($result['errcode']) && $result['errcode'] === 0) {
                $result['card_type'] = $type; // 添加身份证类型标识
            }

            return $result;

        } catch (\Exception $e) {
            Log::error('身份证OCR异常: ' . $e->getMessage());
            return [
                'errcode' => -1,
                'errmsg' => '身份证识别服务异常: ' . $e->getMessage()
            ];
        }
    }

    /**
     * @notes 获取AccessToken
     * @return mixed|string
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getAccessToken()
    {
        $cacheKey = 'wechat_stable_access_token';
        $token = Cache::get($cacheKey);

        if (empty($token)) {
            $response = $this->client->post('/cgi-bin/stable_token', [
                'json' => [
                    'grant_type' => 'client_credential',
                    'appid'      => $this->appId,
                    'secret'     => $this->appSecret,
                ]
            ]);

            $result = json_decode($response->getBody()->getContents(), true);

            if (isset($result['access_token'])) {
                $token = $result['access_token'];
                // 缓存 access_token，有效期提前10分钟
                Cache::set($cacheKey, $token, $result['expires_in'] - 600);
            } else {
                Log::error('获取微信稳定版AccessToken失败: ' . json_encode($result));
                self::$error = $result['errmsg'] ?? '获取稳定版AccessToken失败';
                return false;
            }
        }
        return $token;
    }

    /**
     * @notes 文本内容安全检查
     * @param string $content
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function msgSecCheck(string $content, string $openid): bool
    {
        $accessToken = $this->getAccessToken();
        if (false === $accessToken) {
            return false;
        }
        try {
            $payload = [
                'content' => $content,
                'version' => 2,
                'scene'   => 2,
                'openid'  => $openid
            ];

            $response = $this->client->post(
                '/wxa/msg_sec_check?access_token=' . $accessToken,
                [
                    'headers' => ['Content-Type' => 'application/json; charset=utf-8'],
                    'body' => json_encode($payload, JSON_UNESCAPED_UNICODE)
                ]
            );

            $result = json_decode($response->getBody()->getContents(), true);
        
            if (isset($result['errcode']) && $result['errcode'] === 0) {
                if (isset($result['result']['suggest']) && $result['result']['suggest'] === 'pass') {
                    return true;
                }
                
                self::$error = '内容包含敏感信息，请修改后重试';
                // if(isset($result['result']['label'])){
                    // self::$error .= '(违规类型:'.$result['result']['label'].')';
                // }
                return false;

            } else {
                Log::error('微信文本内容安全检查失败: ' . json_encode($result));
                self::$error = $result['errmsg'] ?? '文本内容安全检查不通过';
                return false;
            }
        } catch (\Exception $e) {
            Log::error('微信文本内容安全检查请求异常: ' . $e->getMessage());
            self::$error = '内容安全服务通讯异常';
            return false;
        }
    }

    /**
     * @notes 媒体内容安全异步检查
     * @param string $mediaUrl
     * @param int $mediaType 1:音频;2:图片
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function mediaCheckAsync(string $mediaUrl, string $openid, int $mediaType = 2, int $scene = 3)
    {
        $accessToken = $this->getAccessToken();
        if (false === $accessToken) {
            return false;
        }

        try {
            $payload = [
                'media_url'  => $mediaUrl,
                'media_type' => $mediaType,
                'version'    => 2,
                'scene'      => $scene,
                'openid'     => $openid
            ];

            $response = $this->client->post(
                '/wxa/media_check_async?access_token=' . $accessToken,
                [
                    'headers' => ['Content-Type' => 'application/json; charset=utf-8'],
                    'body'    => json_encode($payload, JSON_UNESCAPED_UNICODE)
                ]
            );

            $result = json_decode($response->getBody()->getContents(), true);

            if (isset($result['errcode']) && $result['errcode'] == 0) {
                return $result['trace_id'] ?? true; // 返回trace_id
            } else {
                Log::error('微信媒体内容安全检查请求失败: ' . json_encode($result));
                self::$error = $result['errmsg'] ?? '媒体内容安全检查请求失败';
                return false;
            }
        } catch (\Exception $e) {
            Log::error('微信媒体内容安全检查请求异常: ' . $e->getMessage());
            self::$error = '内容安全服务通讯异常';
            return false;
        }
    }
    
    /**
     * @notes 获取错误信息
     * @return mixed
     */
    public static function getError()
    {
        return self::$error;
    }
}