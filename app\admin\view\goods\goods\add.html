{layout name="layout2" /}

<link rel="stylesheet" href="/static/admin/css/goods.css" media="all">
<link href="__PUBLIC__/static/lib/layui/layeditor/layedit.css" rel="stylesheet" />
<script src="__PUBLIC__/static/lib/layui/layeditor/index.js"></script>
<script src="__PUBLIC__/static/lib/layui/layeditor/ace/ace.js"></script>

<style>
/* 资质包样式 */
.qualification-package-container {
    margin-bottom: 20px;
    border: 1px solid #e6e6e6;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.qualification-package-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 15px 20px;
    border-bottom: 1px solid #e6e6e6;
    border-radius: 8px 8px 0 0;
}

.selection-mode-badge {
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    display: inline-block;
}

.mode-all {
    background: #fff2e8;
    color: #fa8c16;
    border: 1px solid #ffd591;
}

.mode-any {
    background: #e6f7ff;
    color: #1890ff;
    border: 1px solid #91d5ff;
}

.qualification-package-content {
    padding: 20px;
}

.qualification-audit-item {
    margin-bottom: 15px;
    padding: 15px;
    border: 1px solid #e6e6e6;
    border-radius: 6px;
    background: #fff;
    transition: all 0.2s ease;
}

.qualification-audit-item:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.qualification-audit-item.approved {
    border-color: #52c41a;
    background: #f6ffed;
}

.qualification-audit-item.rejected {
    border-color: #ff4d4f;
    background: #fff2f0;
}

.qualification-audit-item.pending {
    border-color: #faad14;
    background: #fffbe6;
}

.qualification-audit-item.not-uploaded {
    border-color: #d9d9d9;
    background: #fafafa;
}

.qualification-document-preview {
    max-width: 100px;
    max-height: 80px;
    border-radius: 4px;
    cursor: pointer;
    transition: transform 0.2s;
}

.qualification-document-preview:hover {
    transform: scale(1.05);
}

.qualification-no-document {
    text-align: center;
    padding: 20px;
    color: #999;
    background: #fafafa;
    border-radius: 4px;
    border: 1px dashed #d9d9d9;
}

.qualification-empty {
    text-align: center;
    padding: 40px;
    color: #999;
    background: #fafafa;
    border-radius: 8px;
    border: 1px dashed #d9d9d9;
}
</style>

<script>
    // 全局错误处理
    window.addEventListener('error', function (e) {
        console.error('JavaScript错误:', e.error, e.filename, e.lineno);
    });

    // 立即定义全局函数，确保在任何时候都可用
    window.showExampleImage = function (imagePath, qualificationName) {
        try {
            if (!imagePath || imagePath === 'test.jpg') {
                layer.msg('暂无示例图');
                return;
            }

            var title = (qualificationName ? (qualificationName + ' - 示例图片') : '示例图片');
            var contentHtml =
                '<div style="text-align:center;padding:16px 16px 8px;">' +
                '<img src="' + imagePath + '" alt="' + (qualificationName || '示例图片') + '" ' +
                'style="max-width:100%;height:auto;border:1px solid #eee;border-radius:6px;" ' +
                'onerror="this.onerror=null;this.alt=\'图片无法加载\';this.style.opacity=0.6;">' +
                '<div style="margin-top:10px;color:#666;font-size:12px;word-break:break-all;text-align:left;">' +

                '</div>' +
                '</div>';

            layer.open({
                type: 1,
                title: title,
                area: ['70%', '80%'],
                shade: 0.6,
                move: false,
                scrollbar: true,
                content: contentHtml
            });
        } catch (error) {
            console.error('showExampleImage执行出错:', error);
            layer.msg('示例图片打开失败：' + error.message);
        }
    };

    // 备用函数，防止主函数被覆盖
    window.showExampleImageBackup = window.showExampleImage;

    // 测试函数是否可用
    console.log('showExampleImage函数已定义:', typeof window.showExampleImage);

    // 确保函数在DOM加载后仍然可用
    document.addEventListener('DOMContentLoaded', function () {
        console.log('DOM加载完成，showExampleImage函数状态:', typeof window.showExampleImage);

        // 如果主函数被覆盖，恢复备用函数
        if (typeof window.showExampleImage !== 'function' && typeof window.showExampleImageBackup === 'function') {
            window.showExampleImage = window.showExampleImageBackup;
            console.log('已恢复showExampleImage函数');
        }
    });

    // 页面加载完成后再次检查
    window.addEventListener('load', function () {
        console.log('页面完全加载，showExampleImage函数状态:', typeof window.showExampleImage);
    });
</script>

<style>
    /* 重审按钮样式 */
    .qualification-reaudit-actions {
        margin-top: 10px;
        text-align: center;
    }

    .qualification-reaudit-btn {
        background: #1890ff;
        color: white;
        border: none;
        padding: 6px 12px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        transition: all 0.3s;
    }

    .qualification-reaudit-btn:hover {
        background: #40a9ff;
        transform: translateY(-1px);
    }

    .qualification-reaudit-btn i {
        margin-right: 4px;
    }

    /* 示例按钮样式 */
    .qualification-example-btn {
        background: #52c41a;
        color: white;
        border: none;
        padding: 4px 8px;
        border-radius: 3px;
        cursor: pointer;
        font-size: 11px;
        margin-left: 8px;
        transition: all 0.3s;
    }

    .qualification-example-btn:hover {
        background: #73d13d;
        transform: translateY(-1px);
    }

    .qualification-example-btn i {
        margin-right: 3px;
    }

    /* 重审弹窗内按钮样式 */
    .reaudit-pass-btn,
    .reaudit-reject-btn {
        min-width: 80px;
        height: 36px;
        line-height: 36px;
        padding: 0 15px;
        font-size: 14px;
    }

    .reaudit-pass-btn {
        background: #52c41a;
        border-color: #52c41a;
    }

    .reaudit-pass-btn:hover {
        background: #73d13d;
        border-color: #73d13d;
    }

    .reaudit-reject-btn {
        background: #ff4d4f;
        border-color: #ff4d4f;
    }

    .reaudit-reject-btn:hover {
        background: #ff7875;
        border-color: #ff7875;
    }
</style>

<div class="layui-tab layui-tab-card">
    <!--顶部切换页-->
    <ul class="layui-tab-title">
        <li class="goods-tab layui-this" style="color: #6a6f6c">基础设置</li>
        <li class="goods-tab" style="color: #6a6f6c">规格型号</li>
        <li class="goods-tab" style="color: #6a6f6c">商品详情</li>
        <li class="goods-tab" style="color: #6a6f6c">销售设置</li>
        <li class="goods-tab" style="color: #6a6f6c">资质设置</li>
        <!--        <li class="goods-tab" style="color: #6a6f6c">分销设置</li>-->
    </ul>

    <!--切换内容-->
    <div class="layui-tab-content layui-form">
        <!--基础信息-->
        {include file="goods/goods/goods_base" /}
        <!--规格型号-->
        {include file="goods/goods/goods_spec" /}
        <!--商品详情-->
        {include file="goods/goods/goods_content" /}
        <!--销售设置-->
        {include file="goods/goods/goods_sale_setting" /}
        <!--资质设置-->
        <div class="layui-tab-item">
            <div class="layui-card">
                <div class="layui-card-header">
                    <h3>商品资质设置</h3>
                </div>

                <!--操作提示-->
                <div class="layui-card-body">
                    <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                        <div class="layui-colla-item">
                            <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示
                            </h2>
                            <div class="layui-colla-content layui-show">
                                <p>*商品可以选择继承分类的资质设置，或使用独立的资质设置。</p>
                                <p>*开启"使用独立资质设置"后，商品将不再继承分类的资质要求。</p>
                                <p>*资质包包含一个或多个资质证书，可以设置为"全部需要"或"任选其一"。</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-card-body">
                    <!-- 资质设置开关 -->
                    <div class="layui-form-item">
                        <label class="layui-form-label">资质设置：</label>
                        <div class="layui-input-block">
                            <input type="radio" name="use_custom_qualification" value="0" title="继承分类设置" checked>
                            <input type="radio" name="use_custom_qualification" value="1" title="使用独立设置">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item layui-hide">
            <input type="button" lay-submit lay-filter="goods-submit" id="goods-submit" value="确认">
        </div>
    </div>
</div>

<script src="__PUBLIC__/static/common/js/array.js"></script>

<script>
    var dragstart = 0;
    var swop_element_ed = -1;
    var create_table_by_spec = null;
    var spec_table_data = [];
    var spec_value_temp_id_number = 0;

    layui.config({
        version: "{$front_version}",
        base: '/static/lib/'
    }).extend({
        likeedit: 'likeedit/likeedit'
    }).use(['table', 'form', 'element', 'likeedit'], function () {
        var form = layui.form
            , $ = layui.$
            , element = layui.element
            , layEditor = layui.layEditor;
        //---------------------------------------平台分类联动 begin ----------------------------------
        var categorys = {$category_lists|raw};

    setSelectFirst();

    function setSelectFirst(default_id) {
        var category_select_html = '<option value="">请选择分类</option>';
        for (var i in categorys) {
            if (categorys[i]['pid'] == 0) {
                category_select_html += '<option value="' + categorys[i]['id'] + '">' + categorys[i]['name'] + '</option>';
            }
        }
        $('select[name="first_cate_id"]').html(category_select_html);
        $('select[name="first_cate_id"]').val(default_id);
        form.render('select');
    }

    function setSelectSecond(default_id, pid) {
        pid = pid === undefined ? $('select[name="first_cate_id"]').val() : pid;
        $('select[name="second_cate_id"]').html('<option value="">请选择分类</option>');
        $('select[name="third_cate_id"]').html('<option value="">请选择分类</option>');
        var category_select_html = '<option value="">请选择分类</option>';
        for (var i in categorys) {
            if (categorys[i]['pid'] == pid) {
                category_select_html += '<option value="' + categorys[i]['id'] + '">' + categorys[i]['name'] + '</option>';
            }
        }
        $('select[name="second_cate_id"]').html(category_select_html);
        $('select[name="second_cate_id"]').val(default_id);
        form.render('select');
    }

    function setSelectThird(default_id, pid) {
        pid = pid === undefined ? $('select[name="second_cate_id"]').val() : pid;
        $('select[name="third_cate_id"]').html('<option value="">请选择分类</option>');
        var first_cate_id = $('select[name="first_cate_id"]').val();
        var category_select_html = '<option value="">请选择分类</option>';
        for (var i in categorys) {
            if (categorys[i]['pid'] == pid) {
                category_select_html += '<option value="' + categorys[i]['id'] + '">' + categorys[i]['name'] + '</option>';
            }
        }
        $('select[name="third_cate_id"]').html(category_select_html);
        $('select[name="third_cate_id"]').val(default_id);
        form.render('select');
    }
    // 监听一级分类选择
    form.on('select(first_category)', function (data) {
        setSelectSecond('', data.value);
    });
    // 监听二级分类选择
    form.on('select(second_category)', function (data) {
        setSelectThird('', data.value);
    });

    // 监听三级分类选择 - 加载资质审核
    form.on('select(third_category)', function (data) {
        if (data.value) {
            loadQualificationAudit(data.value);
        } else {
            hideQualificationAudit();
        }
    });
    //---------------------------------------平台分类联动 end ----------------------------------

    // -------------------------------------- 下拉菜单渲染 begin -------------------------------
    var shop_category_lists = {$shop_category_lists|raw}; // 店铺商品分类
    var unit = {$unit_lists|raw};
    var brands = {$brand_lists|raw};
    var supplier = {$supplier_lists|raw};
    var freight = {$freight_lists|raw};

    //渲染商家分类
    like.setSelect('', shop_category_lists, "shop_cate_id", '分类');
    //渲染单位
    like.setSelect('', unit, "unit_id", '单位');
    //渲染品牌
    like.setSelect('', brands, "brand_id", '品牌');
    //渲染供应商
    like.setSelect('', supplier, "supplier_id", '供应商');
    //渲染运费模板
    like.setSelect('', freight, "express_template_id", '运费模板');
    // -------------------------------------- 下拉菜单渲染 end ---------------------------------

    // ----------------------------------------图片/视频上传 begin -----------------------------
    // // 监听图片删除
    // like.delUpload();
    // // 商品封面图
    // $(document).on("click", "#image", function () {
    //     like.imageUpload({
    //         limit: 1,
    //         field: "image",
    //         that: $(this),
    //         content: '{:url("file/lists")}?type=10'
    //     });
    // })
    // // 商品分享海报
    // $(document).on("click", "#poster", function () {
    //     like.imageUpload({
    //         limit: 1,
    //         field: "poster",
    //         that: $(this),
    //         content: '{:url("file/lists")}?type=10'
    //     });
    // })
    // // 商品轮播图
    // $(document).on("click", "#goodsimage", function () {
    //     like.imageUpload({
    //         limit: 5,
    //         field: "goods_image[]",
    //         that: $(this),
    //         content: '/shop/file/lists?type=10'
    //     });
    // })
    // // 商品视频
    // $(document).on("click", "#video", function () {
    //     like.videoUpload({
    //         limit: 1,
    //         field: "video",
    //         that: $(this),
    //         content: '/shop/file/videoList'
    //     });
    // })
    // // 统一规格-规格图片
    // $(document).on("click", "#one_spec_image", function () {
    //     like.imageUpload({
    //         limit: 1,
    //         field: "one_spec_image",
    //         that: $(this),
    //         content: '/shop/file/lists?type=10'
    //     });
    // })
    // // 多规格-规格图片
    // $(document).on("click", ".more_spec_image", function () {
    //     like.imageUpload({
    //         limit: 1,
    //         field: "spec_image[]",
    //         that: $(this),
    //         content: '/shop/file/lists?type=10'
    //     });
    // })
    // // 监听编辑时多规格图片删除按钮
    // $(document).on('mouseenter', '.goods-spec-img-div', function () {
    //     $(this).find('.goods-spec-img-del-x').show();
    // });
    // $(document).on('mouseleave', '.goods-spec-img-div', function () {
    //     $(this).find('.goods-spec-img-del-x').hide();
    // });
    // $(document).on('click', '.goods-spec-img-del-x', function () {
    //     var key = 'spec_image[]' + $(this).parent().parent().parent().attr('spec-value-temp-ids');
    //     $(this).parent().html('<div class="like-upload-image goods-spec-img-div"><div class="upload-image-elem"><a class="add-upload-image more_spec_image"> + 添加图片</a></div></div>');
    //     spec_table_data[key] = '';
    //
    // });
    // 放大图片
    $(document).on('click', 'img', function () {
        like.showImg($(this).attr('src'), 1200);
    });
    // 查看视频
    $(document).on('click', 'video', function () {
        var src = $(this).attr('src');
        layer.open({
            type: 1,
            title: '查看视频',
            content: '<div style="text-align:center;"><video style="width:60%;margin:15px auto;" src="' + src + '" autoplay controls></video></div>',
            area: ['60%', '60%']
        });
    });
    // ----------------------------------------图片/视频上传 end -----------------------------

    //------------------------------------------数据验证 begin -------------------------------
    function switchTab(number) {
        $('.goods-tab').removeClass('layui-this');
        $('.goods-content').removeClass('layui-show');
        $('.goods-tab').eq(number).addClass('layui-this');
        $('.goods-content').eq(number).addClass('layui-show');
    }

    form.verify({
        custom_required: function (value, item) {
            if (!$.trim(value)) {
                switchTab($(item).attr('switch-tab'));
                return $(item).attr('verify-msg');
            }
        },
        status: function (value, item) {
            if (!$('input[name="status"]:checked').val()) {
                return $(item).attr('verify-msg');
            }
        },
        one_spec_required: function (value, item) {
            if ($('input[name="spec_type"]:checked').val() == 1) {
                if (!value) {
                    switchTab($(item).attr('switch-tab'));
                    return $(item).attr('verify-msg');
                }
            }
        },
        add_more_spec: function (value, item) {
            if ($('input[name="spec_type"]:checked').val() == 2) {
                if ($('#more-spec-lists-table tbody tr').length == 0) {
                    switchTab($(item).attr('switch-tab'));
                    return $(item).attr('verify-msg');
                }
            }
        },
        more_spec_required: function (value, item) {
            if ($('input[name="spec_type"]:checked').val() == 2) {
                if (!value) {
                    switchTab($(item).attr('switch-tab'));
                    return $(item).attr('verify-msg');
                }
            }
        },
        one_volume: function (value, item) {
            if ($('input[name="spec_type"]:checked').val() == 1) {
                if (value && value < 0) {
                    switchTab($(item).attr('switch-tab'));
                    return '体积必须大于0';
                }
            }
        },
        one_weight: function (value, item) {
            if ($('input[name="spec_type"]:checked').val() == 1) {
                if (value && value < 0) {
                    switchTab($(item).attr('switch-tab'));
                    return '重量必须大于0';
                }
            }
        },
        one_market_price: function (value, item) {
            if ($('input[name="spec_type"]:checked').val() == 1) {
                if (value && value <= 0) {
                    switchTab($(item).attr('switch-tab'));
                    return '市场价必须大于0';
                }
            }
        },
        one_price: function (value, item) {
            if ($('input[name="spec_type"]:checked').val() == 2) {
                if (value && value <= 0) {
                    switchTab($(item).attr('switch-tab'));
                    return '价格必须大于0';
                }
            }
        },
        one_chengben_price: function (value, item) {
            if ($('input[name="spec_type"]:checked').val() == 2) {
                if (value && value <= 0) {
                    switchTab($(item).attr('switch-tab'));
                    return '成本价必须大于0';
                }
            }
        },
        more_market_price: function (value, item) {
            if ($('input[name="spec_type"]:checked').val() == 2) {
                if (value && value <= 0) {
                    switchTab($(item).attr('switch-tab'));
                    return '市场价必须大于0';
                }
            }
        },
        more_price: function (value, item) {
            if ($('input[name="spec_type"]:checked').val() == 2) {
                if (value && value < 0.01) {
                    switchTab($(item).attr('switch-tab'));
                    return '价格必须大于或等于0.01';
                }
            }
        },
        more_chengben_price: function (value, item) {
            if ($('input[name="spec_type"]:checked').val() == 2) {
                if (value && value <= 0) {
                    switchTab($(item).attr('switch-tab'));
                    return '成本价格必须大于0';
                }
            }
        },
        more_stock: function (value, item) {
            if ($('input[name="spec_type"]:checked').val() == 2) {
                if (value && value < 0) {
                    switchTab($(item).attr('switch-tab'));
                    return '库存必须大于0';
                }
            }
        },
        more_weight: function (value, item) {
            if ($('input[name="spec_type"]:checked').val() == 2) {
                if (value && value < 0) {
                    switchTab($(item).attr('switch-tab'));
                    return '重量必须大于0';
                }
            }
        },
        more_volume: function (value, item) {
            if ($('input[name="spec_type"]:checked').val() == 2) {
                if (value && value < 0) {
                    switchTab($(item).attr('switch-tab'));
                    return '体积必须大于0';
                }
            }
        },
        repetition_spec_name: function (value, item) {
            if ($('input[name="spec_type"]:checked').val() == 2) {
                var spec_names = [];
                $('.spec_name').each(function () {
                    spec_names.push($(this).val());
                });
                if ((new Set(spec_names)).size != spec_names.length) {
                    switchTab($(item).attr('switch-tab'));
                    return '规格名称重复';
                }
            }
        },
        repetition_spec_value: function (value, item) {
            if ($('input[name="spec_type"]:checked').val() == 2) {
                var spec_values = [];
                $(item).find('.goods-spec-value-input').each(function () {
                    spec_values.push($(this).val());
                });
                if ((new Set(spec_values)).size != spec_values.length) {
                    switchTab($(item).attr('switch-tab'));
                    return '同一规格中，规格值不能重复';
                }
            }
        },
        distribution: function (value, item) {
            var first_ratio = parseFloat($('.first_ratio').val());
            var second_ratio = parseFloat($('.second_ratio').val());
            var three_ratio = parseFloat(value);
            if (first_ratio + second_ratio + three_ratio > 100) {
                return '分销比例不可超过100';
            }

        }
    });
    //------------------------------------------数据验证 end ----------------------------------
    //------------------------------------------规格型号 begin --------------------------------
    //监听多规格/单规格按钮
    form.on('radio(spec-type)', function (data) {
        switchSpecType(data.value);
    });

    // 统一规格与多规格切换事件
    function switchSpecType(value) {
        var goods_spec_project = $('#goods-spec-project'); // 规格项区域

        if (value == 2) { // 多规格
            $('#add-spec').parent().show(); // 显示添加规格项目按钮

            if (goods_spec_project.children().length > 0) { // 判断规格项区域是否有子元素
                goods_spec_project.parent().show(); // 显示规格项区域
                $('#more-spec-lists').show(); // 显示多规格明细
            }
            $('#one-spec-lists').hide(); // 隐藏统一规格规格明细
        } else {
            $('#add-spec').parent().hide(); // 隐藏 添加规格项目 按钮
            goods_spec_project.parent().hide(); // 隐藏规格项区域
            $('#more-spec-lists').hide(); // 隐藏多规格明细
            $('#one-spec-lists').show(); // 显示单规格明细
        }
    }

    //监听添加规格项按钮
    // $(document).on('click', '#add-spec', function () {
    //     addSpec();
    // });

    //添加规格项
    function addSpec(value) {
        value = value === undefined ? ' ' : value;
        var element_spec = $('#goods-spec-project'); // 规格项区域
        var count = $('.goods-spec').length; // 规格项数量
        if (count > 2) {
            layer.msg('最多添加3个规格项目');
            return;
        }
        var template_spec = $('#template-spec').html(); // 获取规格项目模板
        // 使用value值替换规格项目模板中{value}占位符，并追加至规格项区域中
        element_spec.append(template_spec.replace('{value}', value));
        $('#goods-spec-project').parent().show();
        form.render('checkbox');
    }

    // 鼠标移入显示删除规格项按钮
    // $(document).on('mouseenter', '.goods-spec', function () {
    //     $(this).find('.goods-spec-del-x').show();
    // });

    // 鼠标移出隐藏删除规格项按钮
    $(document).on('mouseleave', '.goods-spec', function () {
        $(this).find('.goods-spec-del-x').hide();
    });

    // 监听删除规格项目按钮
    $(document).on('click', '.goods-spec-del-x', function () {
        $(this).parent().remove(); // 移除当前规格项目
        var goods_spec_project = $('#goods-spec-project');
        if (goods_spec_project.children().length == 0) { // 规格项区域中若没有子元素则隐藏
            goods_spec_project.parent().hide();
        }
        // 触发生成表格函数
        triggerCreateTableBySepc();
    });

    // 监听规格项输入
    $(document).on('input', '.goods-spec input', function () {
        triggerCreateTableBySepc();
        specValueLater();
    });

    // 触发生成规格明细表格
    function triggerCreateTableBySepc() {
        clearTimeout(create_table_by_spec);
        create_table_by_spec = setTimeout(createTableBySepc, 1000);
    }

    // 生成规格明细表格
    function createTableBySepc() {
        if ($('.goods-spec').length <= 0) { // 没有规格项目，隐藏多规格明细
            $('#more-spec-lists').hide();
            return;
        }
        $('#more-spec-lists').show(); // 显示多规格明细
        var table_title = []; // 用于保存 规格项的值
        var table_data = [];  // 规格项数据
        var spec_value_temp_arr = []; // 规格值临时数组
        var i = 0;
        var table_html = '';
        var th_html = $('#template-spec-table-th').html(); // 多规格表头模板
        var tr_html = $('#template-spec-table-tr').html(); // 多规格行模板

        //遍历规格项目
        $('.goods-spec').each(function () {
            var spec_name = $(this).find('.spec_name').first().val(); // 规格项的值 例：颜色
            if (isEmptyString(spec_name)) {
                return true;
            }
            table_title[i] = spec_name; // 保存 规格项的值  例：['颜色']
            table_data[i] = []; // 例: [[]]
            spec_value_temp_arr[i] = []; // 例：[[]]
            var j = 0;
            // 遍历 当前规格项目 下的所有 规格值
            $(this).find('.goods-spec-value .goods-spec-value-input').each(function () {
                var spec_value = $(this).val(); // 规格值 例：
                var spec_value_temp_id = $(this).attr('spec-value-temp-id'); // 规格值临时id
                if (isEmptyString(spec_value)) {
                    return true;
                }
                table_data[i][j] = spec_value; // 将 规格值 保存至 规格项 中
                spec_value_temp_arr[i][j] = spec_value_temp_id; // 将 规格值临时id 保存至 规格值临时数组 中
                j++;
            });
            i++;
        });

        //表格头部组装
        spec_th_html = '';
        for (var i in table_title) {
            spec_th_html += '<th>' + table_title[i] + '</th>';
        }
        table_html = th_html.replace('{spec_th}', spec_th_html);
        // 笛卡尔积, 组装SKU 例：[['颜色', 'S码'], ['颜色', 'M码']]
        spec_value_temp_arr = cartesianProduct(spec_value_temp_arr);
        table_data = cartesianProduct(table_data);
        for (var i in table_data) {
            var spec_tr_html = '';
            var tr_name_arr = [];
            var specs = '';
            if (Array.isArray(table_data[i])) {
                //根据规格创建tr
                var spec_value_temp_ids = '';
                for (var j in spec_value_temp_arr[i]) {
                    spec_value_temp_ids += spec_value_temp_arr[i][j] + ',';
                }
                spec_value_temp_ids = spec_value_temp_ids.substring(0, spec_value_temp_ids.lastIndexOf(','));
                spec_tr_html += '<tr spec-value-temp-ids="' + spec_value_temp_ids + '">';

                for (var j in table_data[i]) {
                    spec_tr_html += '<td>' + table_data[i][j] + '</td>';
                    tr_name_arr[j] = table_data[i][j];
                    specs += table_data[i][j].replace(',', '') + ',';
                }
            } else {
                var spec_value_temp_ids = spec_value_temp_arr[i];
                spec_tr_html = '<tr spec-value-temp-ids="' + spec_value_temp_ids + '">';
                spec_tr_html += '<td>' + table_data[i] + '</td>';
                specs += table_data[i].replace(',', '') + ',';
            }
            specs = specs.substring(0, specs.lastIndexOf(','));
            spec_table_data["spec_value_str[]" + spec_value_temp_ids] = specs;
            spec_tr_html += '<td style="display: none"><input type="hidden" name="spec_value_str[]" value="' + specs + '"><input type="hidden" name="item_id[]" value=""></td>';
            table_html += tr_html.replace('{spec_td}', spec_tr_html);
        }

        $('#more-spec-lists-table').html(table_html);
        setTableValue();
    };

    //动态渲染已保存的值
    function setTableValue() {
        $('#more-spec-lists-table').find('input').each(function () {
            var key = $(this).attr('name') + $(this).parent().parent().attr('spec-value-temp-ids');
            if (spec_table_data[key] !== undefined) {
                $(this).val(spec_table_data[key]);
            }
        });
        $('.goods-spec-img-div').each(function () {
            var key = $(this).parent().parent().attr('spec-value-temp-ids');
            if (spec_table_data["spec_image[]" + key]) {
                $(this).html('<input name="spec_image[]" type="hidden" value="' + spec_table_data["spec_image[]" + key] + '"><a class="goods-spec-img-del-x">x</a><img class="goods-spec-img" src="' + spec_table_data["spec_image[]" + key] + '">');
            }
        });
    }

    // 监听添加规格值链接被点击: 弹出多行输出框，处理输入的规格值数据，遍历每个规格值并生成相应的html
    // $(document).on('click', '.add-spec-value', function () {
    //     var add_spec_value = $(this);
    //     layer.prompt({title: '输入规格值，多个请换行', formType: 2}, function (text, index) {
    //         layer.close(index);
    //         var specs = text.split('\n');
    //         for (var i in specs) {
    //             specs[i] = specs[i].trim();
    //         }
    //         specs = unique(specs);
    //         var added_specs = [];
    //         add_spec_value.parent().parent().find('.goods-spec-value-input').each(function () {
    //             added_specs.push($(this).val().trim());
    //         });
    //         for (var i in specs) {
    //             var spec = specs[i].trim();
    //             if (spec == '' || in_array(spec, added_specs)) {
    //                 //已存或为空的不添加
    //                 continue;
    //             }
    //             addSpecvalue(add_spec_value, spec, 0);
    //         }
    //         specValueLater();
    //     });
    // });

    // 添加规格值: 将【数据】填充至【规格值模板】，并将【规格值模板】追加至【添加规格值】链接前
    function addSpecvalue(add_spec_value, spec, spec_id) {
        var template_spec_value = $('#template-spec-value').html();
        var template_spec_value_html = template_spec_value.replace('{spec_value_temp_id}', spec_value_temp_id_number--);
        template_spec_value_html = template_spec_value_html.replace('{spec_value_id}', spec_id);
        template_spec_value_html = template_spec_value_html.replace('{spec_value}', spec)
        add_spec_value.parent().before(template_spec_value_html);
    }

    //处理每项规格值
    function specValueLater() {
        $('.add-spec-value').each(function () {
            add_spec_value = $(this);
            var spec_values = '';
            add_spec_value.parent().parent().find('.goods-spec-value-input').each(function () {
                spec_values += $(this).val() + ',';
            });
            add_spec_value.parent().find('.spec_values').val(spec_values.substring(0, spec_values.lastIndexOf(',')));

            var spec_value_ids = '';
            add_spec_value.parent().parent().find('.goods-sepc-value-id-input').each(function () {
                spec_value_ids += $(this).val() + ',';
            });
            add_spec_value.parent().find('.spec_value_ids').val(spec_value_ids.substring(0, spec_value_ids.lastIndexOf(',')));
            triggerCreateTableBySepc();
        });
    }

    // 显示或隐藏 规格值删除按钮
    $(document).on('mouseenter', '.goods-spec-value', function () {
        $(this).find('.goods-spec-value-del-x').show();
    });

    $(document).on('mouseleave', '.goods-spec-value', function () {
        $(this).find('.goods-spec-value-del-x').hide();
    });

    //删除规格值
    $(document).on('click', '.goods-spec-value-del-x', function () {
        var add_spec_value = $(this).parent().parent().find('.add-spec-value').first();
        $(this).parent().remove();
        specValueLater();
        triggerCreateTableBySepc();
    });

    // 监听规格明细输入，规格数据本地保存
    $(document).on('input', '#more-spec-lists-table input', function () {
        var key = $(this).attr('name') + $(this).parent().parent().attr('spec-value-temp-ids');
        spec_table_data[key] = $(this).val();
    });

    //批量填充
    $(document).on('click', '.batch-spec-content', function () {
        var title = $(this).text();
        var input_name = $(this).attr('input-name');
        layer.prompt({
            formType: 3
            , title: '批量填写' + title
        }, function (value, index, elem) {
            $('input[name="' + input_name + '[]"]').val(value);
            //保存值到本地
            $('#more-spec-lists-table input').each(function () {
                var key = $(this).attr('name') + $(this).parent().parent().attr('spec-value-temp-ids');
                spec_table_data[key] = $(this).val();
            });
            layer.close(index);
        });
    });
    //------------------------------------------规格型号 end ------------------------------------

    //------------------------------------------富文本编辑器 begin --------------------------------
    layEditor.set({
        uploadImage: {
            url: '{:url("file/lists")}?type=10'
        },
    })
    var ieditor = layEditor.build('content')
    form.verify({
        content: function (value) {
            return layEditor.sync(ieditor);
        }
    });
    //------------------------------------------富文本编辑器 end --------------------------------


    //------------------------------------------切换商品类型 begin --------------------------------
    // 切换商品类型
    function switchGoodsType(type) {
        if (type == '1') {
            // 虚拟商品
            $('.virtual-goods-data').show();
            $('.actual-goods-data').hide();
            // 配送方式
            $('.delivery_virtual').show();
            $('.delivery_express').hide();
        } else {
            // 实物商品
            $('.virtual-goods-data').hide();
            $('.actual-goods-data').show();
            // 配送方式
            $('.delivery_virtual').hide();
            $('.delivery_express').show();
        }
    }

    // 初始选中配送方式
    function initDeliveryType(type) {
        if (type == '1') {
            $('input[name="delivery_type[]"][value=1]').prop("checked", false);
            $('input[name="delivery_type[]"][value=2]').prop("checked", true);
        } else {
            $('input[name="delivery_type[]"][value=1]').prop("checked", true);
            $('input[name="delivery_type[]"][value=2]').prop("checked", false);
        }
        form.render();
    }

    // 渲染
    function renderDeliveryType(delivery_type, goods_type) {
        delivery_type = delivery_type.split(',');
        if (delivery_type == 'null' || delivery_type == '' || delivery_type.length <= 0) {
            console.log('delivery_type是空的');
            return initDeliveryType(goods_type);
        }
        for (var i = 0; i < delivery_type.length; i++) {
            console.log("input[name=delivery_type][value=" + delivery_type[i] + "]");
            $('input[name="delivery_type[]"][value=' + delivery_type[i] + ']').prop("checked", true);
        }
        form.render();
    }
    //------------------------------------------切换商品类型 end --------------------------------

    //------------------------------------ -----编辑页面 begin --------------------------------
    {notempty name='info'}
    var goods_info = {$info|raw|default=''};

    // 商品类型
    $("input[name=type][value=" + goods_info['base']['type'] + "]").prop("checked", true);
    switchGoodsType(goods_info['base']['type']);
    // 买家付款后 发货后 发货内容
    $("input[name=after_pay][value=" + goods_info['base']['after_pay'] + "]").prop("checked", true);
    $("input[name=after_delivery][value=" + goods_info['base']['after_delivery'] + "]").prop("checked", true);
    $('textarea[name="delivery_content"]').val(goods_info['base']['delivery_content']);
    // 配送方式
    renderDeliveryType(goods_info['base']['delivery_type'], goods_info['base']['type']);

    $('input[name="goods_id"]').val(goods_info['base']['id']);
    $('input[name="name"]').val(goods_info['base']['name']);
    $('input[name="code"]').val(goods_info['base']['code']);
    setSelectFirst(goods_info['base']['first_cate_id']);
    setSelectSecond(goods_info['base']['second_cate_id']);
    setSelectThird(goods_info['base']['third_cate_id']);
    like.setSelect(goods_info['base']['shop_cate_id'], shop_category_lists, "shop_cate_id", '分类');
    $('input[name="remark"]').val(goods_info['base']['remark']);
    like.setSelect(goods_info['base']['unit_id'], unit, "unit_id", '单位');
    like.setSelect(goods_info['base']['brand_id'], brands, "brand_id", '品牌');
    like.setSelect(goods_info['base']['supplier_id'], supplier, "supplier_id", '供应商');

    //渲染商品主图
    if (goods_info['base']['image']) {
        var html = '' +
            '<div class="upload-image-div">' +
            '<img src="' + goods_info['base']['image'] + '" alt="img" />' +
            '<input type="hidden" name="image" value="' + goods_info['base']['image'] + '">' +
            // '<div class="del-upload-btn">x</div>' +
            '</div>' +
            '<div class="upload-image-elem" style="display:none;"><a class="add-upload-image" id="image"> + 添加图片</a></div>';
        $('#imageContainer').html(html);
    }

    //渲染分享海报
    if (goods_info['base']['poster']) {
        var html = '' +
            '<div class="upload-image-div">' +
            '<img src="' + goods_info['base']['poster'] + '" alt="img" />' +
            '<input type="hidden" name="poster" value="' + goods_info['base']['poster'] + '">' +
            // '<div class="del-upload-btn">x</div>' +
            '</div>' +
            '<div class="upload-image-elem" style="display:none;"><a class="add-upload-image" id="image"> + 添加图片</a></div>';
        $('#posterContainer').html(html);
    }

    // 渲染视频
    if (goods_info['base']['video']) {
        var html = '' +
            '<div class="upload-video-div">' +
            '<video src="' + goods_info['base']['video'] + '"></video>' +
            '<input type="hidden" name="video" value="' + goods_info['base']['video'] + '">' +
            // '<div class="del-upload-btn">x</div>' +
            '</div>' +
            '<div class="upload-image-elem" style="display:none;"><a class="add-upload-video" id="video"> + 添加视频</a></div>';
        $('#videoContainer').html(html);
    }

    //渲染商品轮播图
    if (goods_info['base']['goods_image']) {
        var html = '';
        for (j = 0; j < goods_info['base']['goods_image'].length; j++) {
            html = html +
                '<div class="upload-image-div">' +
                '<img src="' + goods_info['base']['goods_image'][j]['abs_image'] + '" alt="img" />' +
                '<input type="hidden" name="goods_image[]" value="' + goods_info['base']['goods_image'][j]['abs_image'] + '">' +
                // '<div class="del-upload-btn">x</div>' +
                '</div>';
        }
        html = html + '<div class="upload-image-elem"><a class="add-upload-image" id="goodsimage"> + 添加图片</a></div>';
        $('#goodsImageContainer').html(html);
    }

    // 规格类型
    $("input[name=spec_type][value=" + goods_info['base']['spec_type'] + "]").prop('checked', "true");


    $('input[name="stock_warn"]').val(goods_info['base']['stock_warn']);
    $("input[name=is_show_stock][value=" + goods_info['base']['is_show_stock'] + "]").prop("checked", true);  //是否显示库存
    $("input[name=express_type][value=" + goods_info['base']['express_type'] + "]").prop("checked", true);
    $('input[name="express_money"]').val(goods_info['base']['express_money']);
    $('input[name="express_money"]').val(goods_info['base']['express_money']);
    like.setSelect(goods_info['base']['express_template_id'], freight, "express_template_id", '运费模板');
    $("input[name=is_member][value=" + goods_info['base']['is_member'] + "]").prop("checked", true);   //会员价是否开启
    $('input[name="sort"]').val(goods_info['base']['sort']);  //商品排序
    $("input[name=is_recommend][value=" + goods_info['base']['is_recommend'] + "]").prop("checked", true); // 是否推荐
    $("input[name=use_custom_qualification][value=" + goods_info['base']['use_custom_qualification'] + "]").prop("checked", true); // 是否独立设置
    $("input[name=status][value=" + goods_info['base']['status'] + "]").prop("checked", true);   //销售状态

    $("input[name=is_distribution][value=" + goods_info['base']['is_distribution'] + "]").prop("checked", true);
    $('input[name="first_ratio"]').val(goods_info['base']['first_ratio']);  //一级分销
    $('input[name="second_ratio"]').val(goods_info['base']['second_ratio']); //二级分销
    $('input[name="third_ratio"]').val(goods_info['base']['third_ratio']); //三级分销

    form.render();

    // 自动检查并加载资质审核信息
    setTimeout(function () {
        var thirdCateId = $('select[name="third_cate_id"]').val();
        if (thirdCateId) {
            loadQualificationAudit(thirdCateId);
        }
    }, 500);

    switchSpecType(goods_info['base']['spec_type']);

    if (goods_info['base']['spec_type'] == 1) { // 单规格
        var html = '' +
            '<div class="upload-image-div">' +
            '<img src="' + goods_info['item'][0]['image'] + '" alt="img" />' +
            '<input type="hidden" name="one_spec_image" value="' + goods_info['item'][0]['image'] + '">' +
            '<div class="del-upload-btn">x</div>' +
            '</div>' +
            '<div class="upload-image-elem" style="display:none;"><a class="add-upload-image" id="one_spec_image"> + 添加图片</a></div>';
        if (goods_info['item'][0]['image']) {
            $('#one_spec_image').parent().parent().html(html);
        }
        $('input[name="one_market_price"]').val(goods_info['item'][0]['market_price']);
        $('input[name="one_price"]').val(goods_info['item'][0]['price']);
        $('input[name="one_chengben_price"]').val(goods_info['item'][0]['chengben_price']);
        $('input[name="one_stock"]').val(goods_info['item'][0]['stock']);
        $('input[name="one_volume"]').val(goods_info['item'][0]['volume']);
        $('input[name="one_weight"]').val(goods_info['item'][0]['weight']);
        $('input[name="one_bar_code"]').val(goods_info['item'][0]['bar_code']);
    }
    if (goods_info['base']['spec_type'] == 2) { // 多规格
        for (var i in goods_info['spec']) {
            addSpec(goods_info['spec'][i]['name']);
            var spes_values = goods_info['spec'][i]['values'];
            for (var j in spes_values) {
                addSpecvalue($('.add-spec-value').eq(i), spes_values[j]['value'], spes_values[j]['id']);
            }

        }
        for (var i in goods_info['spec']) {
            $('input[name="spec_id[]"]').eq(i).val(goods_info['spec'][i]['id']);
        }
        specValueLater();
        createTableBySepc();
        for (var i in goods_info['item']) {
            $('#more-spec-lists-table tbody tr').each(function () {
                var spec_value_str = $(this).find('input[name="spec_value_str[]"]').first().val();
                if (spec_value_str == goods_info['item'][i]['spec_value_str']) {
                    spec_value_temp_ids = $(this).attr('spec-value-temp-ids');
                    spec_table_data["spec_image[]" + spec_value_temp_ids] = goods_info['item'][i]['abs_image'];
                    spec_table_data["price[]" + spec_value_temp_ids] = goods_info['item'][i]['price'];
                    spec_table_data["chengben_price[]" + spec_value_temp_ids] = goods_info['item'][i]['chengben_price'];
                    spec_table_data["market_price[]" + spec_value_temp_ids] = goods_info['item'][i]['market_price'];
                    spec_table_data["stock[]" + spec_value_temp_ids] = goods_info['item'][i]['stock'];
                    spec_table_data["volume[]" + spec_value_temp_ids] = goods_info['item'][i]['volume'];
                    spec_table_data["weight[]" + spec_value_temp_ids] = goods_info['item'][i]['weight'];
                    spec_table_data["bar_code[]" + spec_value_temp_ids] = goods_info['item'][i]['bar_code'];
                    spec_table_data["item_id[]" + spec_value_temp_ids] = goods_info['item'][i]['id'];
                    spec_table_data["spec_value_str[]" + spec_value_temp_ids] = goods_info['item'][i]['spec_value_str'];
                    return false;
                }
            });
        }
        setTableValue();
    }
    layEditor.setContent(ieditor, goods_info['base']['content']);
    form.render();

    // 页面所有元素设置为只读
    setTimeout(function () {
        $("input").attr('readonly', true);
        $("textarea").attr('readonly', true);
        $(':radio').attr('disabled', true);
        $(':checkbox').attr('disabled', true);
        // 保持资质相关按钮与“示例”按钮可用
        $(':button').not('.qualification-audit-btn, .qualification-reaudit-btn, .qualification-example-btn').attr('disabled', true);
        $('a').removeAttr('onclick');

        // 资质审核按钮保持可用状态
        $('.qualification-audit-btn').attr('disabled', false);
        $('.qualification-audit-btn').removeClass('disabled');
        $('.qualification-audit-btn').css('pointer-events', 'auto');
        $('.qualification-audit-btn').css('opacity', '1');

        // 重审按钮保持可用状态
        $('.qualification-reaudit-btn').attr('disabled', false);
        $('.qualification-reaudit-btn').removeClass('disabled');
        $('.qualification-reaudit-btn').css('pointer-events', 'auto');
        $('.qualification-reaudit-btn').css('opacity', '1');

        // 示例按钮保持可用状态
        $('.qualification-example-btn').attr('disabled', false);
        $('.qualification-example-btn').removeClass('disabled');
        $('.qualification-example-btn').css('pointer-events', 'auto');
        $('.qualification-example-btn').css('opacity', '1');

        // 资质设置单选按钮保持可用状态
        $('input[name="use_custom_qualification"]').attr('disabled', false);
        $('input[name="use_custom_qualification"]').removeAttr('readonly');
        $('input[name="use_custom_qualification"]').css('pointer-events', 'auto');
        $('input[name="use_custom_qualification"]').css('opacity', '1');

        // 重新渲染表单以确保layui样式正确
        form.render('radio');
    }, 1500);
    {/notempty}
    //-----------------------------------------编辑页面 end --------------------------------

    // -------------------------------- 资质审核相关函数 begin --------------------------------

    // 审核按钮事件委托
    $(document).on('click', '.qualification-audit-btn', function () {
        var qualificationId = $(this).data('qualification-id');
        var status = $(this).data('status');
        auditQualification(qualificationId, status);
    });

    // 重审按钮事件委托
    $(document).on('click', '.qualification-reaudit-btn', function () {
        var qualificationId = $(this).data('qualification-id');
        showReauditDialog(qualificationId);
    });

    // 移除复杂的事件委托，改用onclick方式

    /**
     * 加载资质审核信息
     */
    function loadQualificationAudit(categoryId) {
        var goodsId = $('input[name="goods_id"]').val();
        if (!goodsId) {
            hideQualificationAudit();
            return;
        }

        $.ajax({
            url: '{:url("goods.goods/getQualificationAudit")}',
            type: 'GET',
            data: {
                category_id: categoryId,
                goods_id: goodsId
            },
            success: function (res) {
                if (res.code === 1 && res.data && res.data.length > 0) {
                    showQualificationAudit(res.data);
                } else {
                    hideQualificationAudit();
                }
            },
            error: function () {
                hideQualificationAudit();
            }
        });
    }

    /**
     * 显示资质审核区域
     */
    function showQualificationAudit(packages) {
        console.log('showQualificationAudit called with:', packages); // 调试日志
        var html = '';

        if (!packages || packages.length === 0) {
            html = `
                <div class="qualification-empty">
                    <div style="text-align: center; padding: 40px; color: #999;">
                        <i class="layui-icon layui-icon-file" style="font-size: 48px; margin-bottom: 15px;"></i>
                        <div>该商品暂无资质要求</div>
                        <div style="font-size: 12px; margin-top: 8px;">请为商品分类或商品设置资质包</div>
                    </div>
                </div>
            `;
        } else {
            packages.forEach(function (package) {
                console.log('Processing package:', package); // 调试日志

                // 资质包头部
                html += `
                    <div class="qualification-package-container" style="margin-bottom: 20px; border: 1px solid #e6e6e6; border-radius: 8px; background: #fff;">
                        <div class="qualification-package-header" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 15px 20px; border-bottom: 1px solid #e6e6e6; border-radius: 8px 8px 0 0;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <h4 style="margin: 0; color: #333; font-size: 16px; font-weight: 600;">${package.package_name}</h4>
                                    <div style="margin-top: 5px; display: flex; align-items: center; gap: 10px;">
                                        <span class="selection-mode-badge ${package.selection_mode == 1 ? 'mode-all' : 'mode-any'}"
                                              style="padding: 3px 8px; border-radius: 12px; font-size: 11px; font-weight: 500;
                                                     ${package.selection_mode == 1 ? 'background: #fff2e8; color: #fa8c16;' : 'background: #e6f7ff; color: #1890ff;'}">
                                            ${package.selection_mode_text}
                                        </span>
                                        ${package.remark ? '<span style="color: #666; font-size: 12px;">' + package.remark + '</span>' : ''}
                                    </div>
                                </div>
                                <div style="color: #666; font-size: 12px; text-align: right;">
                                    <div>${package.selection_mode_desc}</div>
                                </div>
                            </div>
                        </div>
                        <div class="qualification-package-content" style="padding: 20px;">
                `;

                // 资质包内的资质列表
                if (package.qualifications && package.qualifications.length > 0) {
                    package.qualifications.forEach(function (item) {
                        console.log('Processing qualification item:', item); // 调试日志
                        var statusClass = '';
                        var statusText = '';
                        var statusIcon = '';
                        var auditButtons = '';
                        var auditRemarkHtml = '';

                        // 根据资质状态设置样式和按钮
                        switch (parseInt(item.status)) {
                            case 1:
                                // 已通过
                                statusClass = 'approved';
                                statusText = '审核通过';
                                statusIcon = '<i class="layui-icon layui-icon-ok" style="color: #52c41a; font-size: 16px;"></i>';
                                auditButtons = `
                                    <div class="qualification-audit-result approved">
                                        <i class="layui-icon layui-icon-ok" style="font-size: 18px;"></i>
                                        <span>已审核通过</span>
                                        ${item.expire_time_text ? '<span style="margin-left: 10px; font-size: 12px; color: #666;">有效期至: ' + item.expire_time_text + '</span>' : ''}
                                    </div>
                                    <div class="qualification-reaudit-actions">
                                        <button class="qualification-reaudit-btn" data-qualification-id="${item.shop_qualification_id}">
                                            <i class="layui-icon layui-icon-refresh"></i>重审
                                        </button>
                                    </div>
                                `;
                                break;
                            case 2:
                                // 未通过
                                statusClass = 'rejected';
                                statusText = '审核未通过';
                                statusIcon = '<i class="layui-icon layui-icon-close" style="color: #ff4d4f; font-size: 16px;"></i>';
                                auditButtons = `
                                        <i class="layui-icon layui-icon-close" style="font-size: 18px;"></i>
                                        <span>审核未通过</span>
                                        ${item.audit_remark ? '<div style="margin-top: 8px; color: #ff4d4f; font-size: 12px;">原因: ' + item.audit_remark + '</div>' : ''}
                                    </div>
                                    <div class="qualification-reaudit-actions">
                                        <button class="qualification-reaudit-btn" data-qualification-id="${item.shop_qualification_id}">
                                            <i class="layui-icon layui-icon-refresh"></i>重审
                                        </button>
                                    </div>
                                `;
                                if (item.audit_remark) {
                                    auditRemarkHtml = '<div class="audit-remark-display"><strong>审核未通过原因:</strong> ' + item.audit_remark + '</div>';
                                }
                                break;
                            default:
                                // 待审核或未上传
                                if (item.shop_qualification_id > 0) {
                                    // 已上传，待审核
                                    statusClass = 'pending';
                                    statusText = '待审核';
                                    statusIcon = '<i class="layui-icon layui-icon-time" style="color: #faad14; font-size: 16px;"></i>';
                                    auditButtons = generateAuditButtons(item.shop_qualification_id, '审核');
                                } else {
                                    // 未上传
                                    statusClass = 'not-uploaded';
                                    statusText = '未上传';
                                    statusIcon = '<i class="layui-icon layui-icon-close" style="color: #ff4d4f; font-size: 16px;"></i>';
                                    auditButtons = '<div class="qualification-audit-result not-uploaded">商家未上传此资质</div>';
                                }
                                break;
                        }

                        var documentHtml = '';
                        if (item.document_path) {
                            documentHtml = `
                                <div class="qualification-document">
                                    <div class="qualification-document-item">
                                        <img src="${item.document_path}" class="qualification-document-preview" alt="资质文件">
                                        <div class="qualification-document-info">
                                            <div class="qualification-document-name">${item.document_name || '资质文件'}</div>
                                            <div class="qualification-document-meta">
                                                ${item.is_goods_specific ? '<span style="color: #1890ff;">商品专用资质</span>' : '<span style="color: #666;">通用资质</span>'}
                                                ${item.expire_time_text ? ' | 有效期至: ' + item.expire_time_text : ''}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `;
                        } else {
                            documentHtml = `
                                <div class="qualification-no-document">
                                    <i class="layui-icon layui-icon-file" style="font-size: 24px; color: #d9d9d9; margin-bottom: 8px;"></i>
                                    <div>商家未上传此资质</div>
                                </div>
                            `;
                        }

                        html += `
                            <div class="qualification-audit-item ${statusClass}" style="margin-bottom: 15px; padding: 15px; border: 1px solid #e6e6e6; border-radius: 6px; background: #fff;">
                                <div class="qualification-audit-header" style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 12px;">
                                    <div class="qualification-audit-info">
                                        <div class="qualification-audit-title" style="font-size: 14px; font-weight: 600; color: #333; margin-bottom: 4px;">
                                            ${statusIcon} ${item.name}
                                        </div>
                                        <div class="qualification-audit-desc" style="color: #666; font-size: 12px; margin-bottom: 6px;">
                                            ${item.description || '无描述'}
                                        </div>
                                        <div class="qualification-audit-meta" style="color: #999; font-size: 11px;">
                                            有效期: ${item.valid_days_text}
                                            ${item.is_goods_specific ? ' | <span style="color: #1890ff;">商品专用</span>' : ' | <span style="color: #666;">通用资质</span>'}
                                        </div>
                                    </div>
                                    <div class="qualification-audit-status" style="text-align: right;">
                                        <div class="qualification-audit-status-text" style="font-size: 12px; font-weight: 600; color: ${statusClass === 'approved' ? '#52c41a' : statusClass === 'rejected' ? '#ff4d4f' : statusClass === 'pending' ? '#faad14' : '#999'};">
                                            ${statusText}
                                        </div>
                                    </div>
                                </div>

                                <div class="qualification-audit-content" style="display: flex; gap: 15px;">
                                    <div class="qualification-audit-document" style="flex: 1;">
                                        ${documentHtml}
                                    </div>
                                    <div class="qualification-audit-actions" style="min-width: 180px;">
                                        ${auditButtons}
                                        ${auditRemarkHtml}
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                } else {
                    html += `
                        <div style="text-align: center; padding: 20px; color: #999;">
                            <i class="layui-icon layui-icon-file" style="font-size: 24px; margin-bottom: 8px;"></i>
                            <div>该资质包暂无资质项目</div>
                        </div>
                    `;
                }

                // 关闭资质包容器
                html += `
                        </div>
                    </div>
                `;
            });
        }

        $('#qualification-audit-list').html(html);
        $('#qualification-audit-area').show();
        // 使用onclick方式，不需要额外的事件绑定
    }

    /**
     * 生成审核按钮
     */
    function generateAuditButtons(qualificationId, buttonText) {
        buttonText = buttonText || '审核';
        return `
                <div class="qualification-audit-actions">
                    <div class="audit-buttons-group">
                        <button class="qualification-audit-btn approve" data-qualification-id="${qualificationId}" data-status="1">
                            <i class="layui-icon layui-icon-ok"></i>通过
                        </button>
                        <button class="qualification-audit-btn reject" data-qualification-id="${qualificationId}" data-status="2">
                            <i class="layui-icon layui-icon-close"></i>不通过
                        </button>
                    </div>
                    <div class="audit-buttons-label">${buttonText}操作</div>
                </div>
            `;
    }

    /**
     * 隐藏资质审核区域
     */
    function hideQualificationAudit() {
        $('#qualification-audit-area').hide();
    }

    /**
     * 审核资质
     */
    function auditQualification(qualificationId, status) {
        var statusText = status === 1 ? '通过' : '不通过';

        if (status === 1) {
            // 通过审核
            layer.confirm('确定通过此资质审核吗？', {
                icon: 3,
                title: '审核确认'
            }, function (index) {
                submitAudit(qualificationId, status, '', index);
            });
        } else {
            // 不通过审核，需要输入原因
            layer.prompt({
                title: '请输入审核不通过的原因',
                formType: 2,
                value: '',
                area: ['450px', '250px'],
                maxlength: 500
            }, function (value, index) {
                if (!value || !value.trim()) {
                    layer.msg('请输入不通过的原因', { icon: 2 });
                    return false;
                }
                if (value.trim().length < 5) {
                    layer.msg('不通过原因至少需要5个字符', { icon: 2 });
                    return false;
                }
                submitAudit(qualificationId, status, value.trim(), index);
            });
        }
    }

    /**
     * 提交审核结果
     */
    function submitAudit(qualificationId, status, remark, layerIndex) {
        $.ajax({
            url: '{:url("goods.goods/auditQualification")}',
            type: 'POST',
            data: {
                qualification_id: qualificationId,
                audit_status: status,
                audit_remark: remark
            },
            success: function (res) {
                layer.close(layerIndex);
                if (res.code === 1) {
                    layer.msg('审核成功', { icon: 1 });
                    // 立即更新当前资质的显示状态
                    updateQualificationStatus(qualificationId, status, remark);
                } else {
                    layer.msg(res.msg || '审核失败', { icon: 2 });
                }
            },
            error: function () {
                layer.close(layerIndex);
                layer.msg('网络错误，请重试', { icon: 2 });
            }
        });
    }

    /**
     * 显示重审弹窗
     */
    function showReauditDialog(qualificationId) {
        var content = `
                <div style="padding: 20px;">
                    <div style="text-align: center; margin-bottom: 20px;">
                        <p style="font-size: 16px; color: #333;">请选择重审结果：</p>
                    </div>
                    <div style="text-align: center; margin-bottom: 20px;">
                        <button type="button" class="layui-btn layui-btn-normal reaudit-pass-btn" data-qualification-id="${qualificationId}">
                            <i class="layui-icon layui-icon-ok"></i> 通过
                        </button>
                        <button type="button" class="layui-btn layui-btn-danger reaudit-reject-btn" data-qualification-id="${qualificationId}" style="margin-left: 10px;">
                            <i class="layui-icon layui-icon-close"></i> 不通过
                        </button>
                    </div>
                    <div style="margin-top: 15px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: bold;">重审理由：</label>
                        <textarea id="reaudit-reason" placeholder="请输入重审理由..." style="width: 100%; height: 80px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; resize: vertical;"></textarea>
                        <div style="font-size: 12px; color: #999; margin-top: 5px;">选择"不通过"时必须填写理由</div>
                    </div>
                </div>
            `;

        layer.open({
            type: 1,
            title: '重审资质',
            content: content,
            area: ['450px', '320px'],
            btn: false,
            success: function (layero, index) {
                // 通过按钮点击事件
                $(layero).find('.reaudit-pass-btn').on('click', function () {
                    var reason = $('#reaudit-reason').val().trim();
                    submitReaudit(qualificationId, 1, reason, index);
                });

                // 不通过按钮点击事件
                $(layero).find('.reaudit-reject-btn').on('click', function () {
                    var reason = $('#reaudit-reason').val().trim();
                    if (!reason) {
                        layer.msg('选择不通过时必须填写理由', { icon: 2 });
                        return false;
                    }
                    if (reason.length < 5) {
                        layer.msg('重审理由至少需要5个字符', { icon: 2 });
                        return false;
                    }
                    submitReaudit(qualificationId, 2, reason, index);
                });
            }
        });
    }

    /**
     * 提交重审结果
     */
    function submitReaudit(qualificationId, status, reason, layerIndex) {
        $.ajax({
            url: '{:url("goods.goods/auditQualification")}',
            type: 'POST',
            data: {
                qualification_id: qualificationId,
                audit_status: status,
                audit_remark: reason
            },
            success: function (res) {
                layer.close(layerIndex);
                if (res.code === 1) {
                    layer.msg('重审成功', { icon: 1 });
                    // 立即更新当前资质的显示状态
                    updateQualificationStatus(qualificationId, status, reason);
                } else {
                    layer.msg(res.msg || '重审失败', { icon: 2 });
                }
            },
            error: function () {
                layer.close(layerIndex);
                layer.msg('网络错误，请重试', { icon: 2 });
            }
        });
    }

    /**
     * 更新资质审核状态显示
     */
    function updateQualificationStatus(qualificationId, status, remark) {
        // 找到对应的资质项
        var $qualificationItem = $('.qualification-audit-actions').filter(function () {
            return $(this).find('[data-qualification-id="' + qualificationId + '"]').length > 0;
        }).closest('.qualification-audit-item');

        if ($qualificationItem.length === 0) {
            return;
        }

        // 移除原有的审核按钮和状态
        $qualificationItem.find('.qualification-audit-actions').remove();
        $qualificationItem.find('.qualification-reaudit-actions').remove();
        $qualificationItem.find('.qualification-audit-result').remove();
        $qualificationItem.find('.audit-remark-display').remove();

        // 更新状态显示
        var $statusDiv = $qualificationItem.find('.qualification-status');
        var $header = $qualificationItem.find('.qualification-item-header');

        if (status == 1) {
            // 审核通过
            $qualificationItem.removeClass('pending rejected').addClass('approved');
            $statusDiv.removeClass('pending rejected').addClass('approved');
            $statusDiv.html('<i class="layui-icon layui-icon-ok" style="color: #52c41a; font-size: 16px;"></i> 审核通过');

            // 添加通过状态显示
            var approvedHtml = `
                    <div class="qualification-audit-result approved">
                        <i class="layui-icon layui-icon-ok" style="font-size: 18px;"></i>
                        <span>已审核通过</span>
                        <span style="margin-left: 10px; font-size: 12px; color: #666;">审核时间: ${getCurrentTime()}</span>
                    </div>
                    <div class="qualification-reaudit-actions">
                        <button class="qualification-reaudit-btn" data-qualification-id="${qualificationId}">
                            <i class="layui-icon layui-icon-refresh"></i>重审
                        </button>
                    </div>
                `;
            $qualificationItem.append(approvedHtml);

        } else if (status == 2) {
            // 审核不通过
            $qualificationItem.removeClass('pending approved').addClass('rejected');
            $statusDiv.removeClass('pending approved').addClass('rejected');
            $statusDiv.html('<i class="layui-icon layui-icon-close" style="color: #ff4d4f; font-size: 16px;"></i> 审核未通过');

            // 添加不通过状态显示和原因
            var rejectedHtml = `
                    <div class="qualification-audit-result rejected">
                        <i class="layui-icon layui-icon-close" style="font-size: 18px;"></i>
                        <span>审核未通过</span>
                        <span style="margin-left: 10px; font-size: 12px; color: #666;">审核时间: ${getCurrentTime()}</span>
                    </div>
                `;
            if (remark) {
                rejectedHtml += `
                        <div class="audit-remark-display">
                            <strong>审核未通过原因:</strong> ${remark}
                        </div>
                    `;
            }
            rejectedHtml += `
                    <div class="qualification-reaudit-actions">
                        <button class="qualification-reaudit-btn" data-qualification-id="${qualificationId}">
                            <i class="layui-icon layui-icon-refresh"></i>重审
                        </button>
                    </div>
                `;
            $qualificationItem.append(rejectedHtml);
        }
    }

    /**
     * 获取当前时间字符串
     */
    function getCurrentTime() {
        var now = new Date();
        var year = now.getFullYear();
        var month = String(now.getMonth() + 1).padStart(2, '0');
        var day = String(now.getDate()).padStart(2, '0');
        var hours = String(now.getHours()).padStart(2, '0');
        var minutes = String(now.getMinutes()).padStart(2, '0');
        var seconds = String(now.getSeconds()).padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }

    // -------------------------------- 资质审核相关函数 end --------------------------------

    // -------------------------------- 资质设置相关函数 begin --------------------------------

    // 加载资质设置数据
    function loadQualificationSettings() {
        var goodsId = getGoodsId();
        if (!goodsId) return;

        // 加载商品当前的资质设置
        like.ajax({
            url: '{:url("goods.goods/getQualificationSettings")}',
            data: { goods_id: goodsId },
            type: "get",
            success: function (res) {
                if (res.code == 1) {
                    var data = res.data;

                    // 设置资质设置开关
                    $('input[name="use_custom_qualification"][value="' + (data.use_custom_qualification || 0) + '"]').prop('checked', true);
                    form.render('radio');

                    // 显示分类资质包
                    renderCategoryPackages(data.category_packages || []);

                    // 显示可用资质包
                    renderAvailablePackages(data.available_packages || [], data.current_overrides || []);

                    // 根据设置显示/隐藏相应区域
                    toggleQualificationSettings(data.use_custom_qualification || 0);
                }
            }
        });
    }

    // 渲染分类资质包
    function renderCategoryPackages(packages) {
        var html = '';
        if (packages && packages.length > 0) {
            packages.forEach(function (package) {
                html += '<div style="margin-bottom: 10px; padding: 8px; background-color: #f8f8f8; border-radius: 4px;">';
                html += '<div style="font-weight: bold; color: #333;">' + package.name + '</div>';
                html += '<div style="font-size: 12px; color: #666;">';
                html += '选择模式: ' + (package.selection_mode == 1 ? '全部需要' : '任选其一');
                html += '</div>';
                if (package.qualifications && package.qualifications.length > 0) {
                    html += '<div style="margin-top: 5px;">';
                    package.qualifications.forEach(function (qual) {
                        html += '<span class="layui-badge layui-bg-blue" style="margin: 2px;">' + qual.name + '</span>';
                    });
                    html += '</div>';
                }
                html += '</div>';
            });
        } else {
            html = '<div style="color: #999;">该商品分类暂未设置资质包要求</div>';
        }
        $('#category-packages-content').html(html);
    }

    // 渲染可用资质包
    function renderAvailablePackages(packages, currentOverrides) {
        var html = '';
        if (packages && packages.length > 0) {
            packages.forEach(function (package, index) {
                var isChecked = currentOverrides.some(function (override) {
                    return override.package_id == package.id;
                });
                var currentOverride = currentOverrides.find(function (override) {
                    return override.package_id == package.id;
                });
                var isRequired = currentOverride ? currentOverride.is_required : 1;

                html += '<div style="margin-bottom: 15px; padding: 10px; border: 1px solid #e6e6e6; border-radius: 4px;">';
                html += '<div style="margin-bottom: 8px;">';
                html += '<input type="checkbox" name="packages[' + index + '][package_id]" value="' + package.id + '" ';
                html += 'lay-skin="primary" title="' + package.name + '" class="package-checkbox"' + (isChecked ? ' checked' : '') + '>';
                html += '</div>';

                html += '<div style="margin-left: 20px; margin-bottom: 10px;">';
                html += '<label style="font-weight: normal;">';
                html += '<input type="radio" name="packages[' + index + '][is_required]" value="1" title="必选"' + (isRequired == 1 ? ' checked' : '') + '>';
                html += '<input type="radio" name="packages[' + index + '][is_required]" value="0" title="可选"' + (isRequired == 0 ? ' checked' : '') + '>';
                html += '</label>';
                html += '</div>';

                html += '<div style="margin-left: 20px; color: #666; font-size: 12px;">';
                html += '<div>选择模式: ' + (package.selection_mode == 1 ? '全部需要' : '任选其一') + '</div>';
                if (package.qualifications && package.qualifications.length > 0) {
                    html += '<div>包含资质: ';
                    package.qualifications.forEach(function (qual) {
                        html += '<span class="layui-badge layui-bg-blue" style="margin: 2px;">' + qual.name + '</span>';
                    });
                    html += '</div>';
                }
                if (package.remark) {
                    html += '<div>说明: ' + package.remark + '</div>';
                }
                html += '</div>';
                html += '</div>';
            });
        } else {
            html = '<div class="layui-text" style="color: #999;">暂无可用的资质包</div>';
        }
        $('#custom-packages-content').html(html);
        form.render();
    }

    // 切换资质设置显示
    function toggleQualificationSettings(useCustom) {
        if (useCustom == 1) {
            $('#category-packages').hide();
            $('#custom-packages').show();
        } else {
            $('#category-packages').show();
            $('#custom-packages').hide();
        }
    }

    // 监听资质设置开关变化
    form.on('radio()', function (data) {
        if (data.elem.name === 'use_custom_qualification') {
            toggleQualificationSettings(data.value);
        }
    });

   
    function saveQualificationSettings() {
        var goodsId = getGoodsId();
        if (!goodsId) return;

        var useCustom = $('input[name="use_custom_qualification"]:checked').val() || 0;
        var packages = [];

        if (useCustom == 1) {
            $('.package-checkbox:checked').each(function () {
                var index = $(this).attr('name').match(/\[(\d+)\]/)[1];
                var packageId = $(this).val();
                var isRequired = $('input[name="packages[' + index + '][is_required]"]:checked').val() || 1;

                packages.push({
                    package_id: packageId,
                    is_required: isRequired
                });
            });
        }

        like.ajax({
            url: '{:url("goods.goods/saveQualificationSettings")}',
            data: {
                goods_id: goodsId,
                use_custom_qualification: useCustom,
                packages: packages
            },
            type: "post",
            success: function (res) {
                if (res.code == 1) {
                    layer.msg('资质设置保存成功', { icon: 1, time: 1000 });
                } else {
                    layer.msg(res.msg || '保存失败', { icon: 2, time: 2000 });
                }
            }
        });
    }

    // 获取商品ID
    function getGoodsId() {
        var info = {$info|raw};
    return info && info.id ? info.id : null;
        }

    // 页面加载时初始化资质设置
    setTimeout(function () {
        loadQualificationSettings();
    }, 1000);

        // -------------------------------- 资质设置相关函数 end --------------------------------

    });

    // 函数已在页面顶部定义
</script>