<!-- 优化的资质选择组件 -->
<style>
:root {
  --primary-color: #1890ff;
  --primary-light: #40a9ff;
  --primary-lighter: #e6f7ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  --text-primary: #262626;
  --text-secondary: #595959;
  --text-disabled: #bfbfbf;
  --border-color: #d9d9d9;
  --border-light: #f0f0f0;
  --bg-light: #fafafa;
  --bg-white: #ffffff;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.1);
  --radius-small: 4px;
  --radius-medium: 8px;
  --radius-large: 12px;
}

.qualification-selector {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
}

.selector-toolbar {
  background: linear-gradient(135deg, var(--bg-light) 0%, #f5f5f5 100%);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-medium);
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: var(--shadow-light);
}

.toolbar-row {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  margin-bottom: 12px;
}

.toolbar-row:last-child {
  margin-bottom: 0;
}

.search-input {
  flex: 1;
  min-width: 200px;
  max-width: 300px;
  height: 36px;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  font-size: 14px;
  transition: all 0.2s;
  background: var(--bg-white);
}

.search-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  outline: none;
}

.filter-select {
  min-width: 120px;
  height: 36px;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  background: var(--bg-white);
  font-size: 14px;
  cursor: pointer;
}

.action-btn {
  height: 36px;
  padding: 8px 16px;
  border: none;
  border-radius: var(--radius-small);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
}

.btn-secondary {
  background: var(--bg-white);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.stats-info {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-number {
  color: var(--primary-color);
  font-weight: 600;
  font-size: 16px;
}

.selected-tags-container {
  background: var(--bg-white);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-medium);
  padding: 16px;
  margin-bottom: 16px;
  min-height: 60px;
  box-shadow: var(--shadow-light);
}

.tags-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.tags-title {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

.clear-all-btn {
  font-size: 12px;
  color: var(--error-color);
  cursor: pointer;
  padding: 4px 8px;
  border-radius: var(--radius-small);
  transition: all 0.2s;
}

.clear-all-btn:hover {
  background: rgba(255, 77, 79, 0.1);
}

.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.selected-tag {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
  border-radius: 18px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
  animation: slideIn 0.3s ease-out;
}

.selected-tag:hover {
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
}

.tag-remove {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  transition: all 0.2s;
}

.tag-remove:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.empty-state {
  color: var(--text-disabled);
  font-style: italic;
  padding: 12px;
  text-align: center;
  background: var(--bg-light);
  border-radius: var(--radius-small);
}

.qualification-list-container {
  background: var(--bg-white);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-medium);
  overflow: hidden;
  box-shadow: var(--shadow-light);
}

.list-header {
  background: linear-gradient(135deg, var(--bg-light) 0%, #f5f5f5 100%);
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-light);
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

.qualification-list {
  max-height: 400px;
  overflow-y: auto;
  padding: 8px;
}

.qualification-list::-webkit-scrollbar {
  width: 6px;
}

.qualification-list::-webkit-scrollbar-track {
  background: var(--bg-light);
  border-radius: 3px;
}

.qualification-list::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.qualification-list::-webkit-scrollbar-thumb:hover {
  background: var(--text-disabled);
}

.qualification-item {
  display: block;
  margin: 6px 0;
  padding: 16px;
  border: 2px solid var(--border-light);
  border-radius: var(--radius-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--bg-white);
  position: relative;
  overflow: hidden;
}

.qualification-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(24, 144, 255, 0.1), transparent);
  transition: left 0.5s;
}

.qualification-item:hover::before {
  left: 100%;
}

.qualification-item:hover {
  border-color: var(--primary-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.qualification-item.selected {
  background: linear-gradient(135deg, var(--primary-lighter) 0%, #f0f9ff 100%);
  border-color: var(--primary-color);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
}

.item-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.item-checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: var(--primary-color);
}

.item-info {
  flex: 1;
}

.item-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 15px;
  margin-bottom: 6px;
  line-height: 1.4;
}

.item-description {
  font-size: 13px;
  color: var(--text-secondary);
  line-height: 1.4;
  margin-bottom: 8px;
}

.item-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.validity-badge {
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  background: var(--primary-lighter);
  color: var(--primary-color);
}

.validity-badge.permanent {
  background: #f6ffed;
  color: var(--success-color);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-input {
    max-width: none;
  }
  
  .stats-info {
    justify-content: center;
  }
  
  .qualification-list {
    max-height: 300px;
  }
  
  .item-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .item-checkbox {
    align-self: flex-start;
  }
}
</style>

<!-- 资质选择器主体 -->
<div class="qualification-selector">
  <!-- 工具栏 -->
  <div class="selector-toolbar">
    <!-- 第一行：搜索和筛选 -->
    <div class="toolbar-row">
      <input type="text" id="qualification-search" class="search-input" placeholder="🔍 输入关键词搜索资质...">
      <select id="validity-filter" class="filter-select">
        <option value="">全部有效期</option>
        <option value="permanent">永久有效</option>
        <option value="temporary">限期有效</option>
      </select>
      <select id="category-filter" class="filter-select">
        <option value="">全部分类</option>
        <option value="business">营业资质</option>
        <option value="technical">技术资质</option>
        <option value="safety">安全资质</option>
        <option value="quality">质量资质</option>
      </select>
    </div>

    <!-- 第二行：操作按钮和统计 -->
    <div class="toolbar-row">
      <button type="button" class="action-btn btn-primary" onclick="selectAll()">
        <i class="layui-icon layui-icon-ok"></i>
        全选
      </button>
      <button type="button" class="action-btn btn-secondary" onclick="selectNone()">
        <i class="layui-icon layui-icon-close"></i>
        清空
      </button>
      <button type="button" class="action-btn btn-secondary" onclick="selectReverse()">
        <i class="layui-icon layui-icon-refresh"></i>
        反选
      </button>
      <button type="button" class="action-btn btn-secondary" onclick="showQuickTemplates()">
        <i class="layui-icon layui-icon-template"></i>
        快速模板
      </button>

      <div class="stats-info">
        <div class="stat-item">
          <span>已选:</span>
          <span class="stat-number" id="selected-count">0</span>
          <span>个</span>
        </div>
        <div class="stat-item">
          <span>总计:</span>
          <span class="stat-number" id="total-count">0</span>
          <span>个</span>
        </div>
        <div class="stat-item">
          <span>显示:</span>
          <span class="stat-number" id="visible-count">0</span>
          <span>个</span>
        </div>
      </div>
    </div>
  </div>

  <!-- 已选择的资质标签 -->
  <div class="selected-tags-container">
    <div class="tags-header">
      <span class="tags-title">已选择的资质</span>
      <span class="clear-all-btn" onclick="selectNone()" style="display: none;" id="clear-all-btn">
        <i class="layui-icon layui-icon-delete"></i>
        清空全部
      </span>
    </div>
    <div class="selected-tags" id="selected-tags">
      <div class="empty-state">
        <i class="layui-icon layui-icon-tips"></i>
        暂未选择任何资质，请在下方列表中选择
      </div>
    </div>
  </div>

  <!-- 资质选择列表 -->
  <div class="qualification-list-container">
    <div class="list-header">
      <span>可选择的资质 (点击卡片或复选框进行选择)</span>
    </div>
    <div class="qualification-list" id="qualification-list">
      {volist name="qualifications" id="qualification"}
      <label class="qualification-item"
             data-id="{$qualification.id}"
             data-name="{$qualification.name}"
             data-description="{$qualification.description|default='无描述'}"
             data-valid-days="{$qualification.valid_days}"
             data-category="{$qualification.category|default='business'}"
             tabindex="0">
        <div class="item-content">
          <input type="checkbox"
                 name="qualification_ids[]"
                 value="{$qualification.id}"
                 class="item-checkbox"
                 {if isset($checked_ids) && in_array($qualification.id, $checked_ids)}checked{/if}>
          <div class="item-info">
            <div class="item-name">{$qualification.name}</div>
            <div class="item-description">{$qualification.description|default='无描述'}</div>
            <div class="item-meta">
              <span class="validity-badge {if $qualification.valid_days == 0}permanent{/if}">
                {if $qualification.valid_days == 0}
                  <i class="layui-icon layui-icon-ok-circle"></i> 永久有效
                {else}
                  <i class="layui-icon layui-icon-time"></i> {$qualification.valid_days}天有效
                {/if}
              </span>
            </div>
          </div>
        </div>
      </label>
      {/volist}
    </div>
  </div>

  <!-- 提示信息 -->
  <div class="layui-form-mid layui-word-aux" style="margin-top: 12px; text-align: center; color: var(--text-secondary);">
    <i class="layui-icon layui-icon-tips"></i>
    请至少选择一个资质证书。支持键盘快捷键：Ctrl+A 全选，Ctrl+D 清空，Ctrl+I 反选
  </div>
</div>

<script>
// 资质选择器类
class QualificationSelector {
  constructor() {
    this.searchInput = document.getElementById('qualification-search');
    this.validityFilter = document.getElementById('validity-filter');
    this.categoryFilter = document.getElementById('category-filter');
    this.selectedTags = document.getElementById('selected-tags');
    this.selectedCount = document.getElementById('selected-count');
    this.totalCount = document.getElementById('total-count');
    this.visibleCount = document.getElementById('visible-count');
    this.clearAllBtn = document.getElementById('clear-all-btn');
    this.qualificationList = document.getElementById('qualification-list');

    this.searchTimeout = null;
    this.templates = {
      'basic': { name: '基础资质包', ids: [] },
      'advanced': { name: '高级资质包', ids: [] },
      'complete': { name: '完整资质包', ids: [] }
    };

    this.init();
  }

  init() {
    this.bindEvents();
    this.updateCounts();
    this.updateSelectedDisplay();
    this.initKeyboardShortcuts();

    // 初始化样式
    this.updateAllItemStyles();

    // 加载保存的选择状态
    this.loadSavedState();
  }

  bindEvents() {
    // 搜索功能（防抖）
    this.searchInput.addEventListener('input', (e) => {
      clearTimeout(this.searchTimeout);
      this.searchTimeout = setTimeout(() => {
        this.performSearch(e.target.value);
      }, 300);
    });

    // 筛选功能
    this.validityFilter.addEventListener('change', () => this.applyFilters());
    this.categoryFilter.addEventListener('change', () => this.applyFilters());

    // 复选框变化监听
    this.qualificationList.addEventListener('change', (e) => {
      if (e.target.type === 'checkbox') {
        this.handleCheckboxChange(e.target);
      }
    });

    // 点击卡片选择
    this.qualificationList.addEventListener('click', (e) => {
      const item = e.target.closest('.qualification-item');
      if (item && e.target.type !== 'checkbox') {
        const checkbox = item.querySelector('input[type="checkbox"]');
        if (checkbox) {
          checkbox.checked = !checkbox.checked;
          this.handleCheckboxChange(checkbox);
        }
      }
    });

    // 键盘导航
    this.qualificationList.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        const item = e.target.closest('.qualification-item');
        if (item) {
          const checkbox = item.querySelector('input[type="checkbox"]');
          if (checkbox) {
            checkbox.checked = !checkbox.checked;
            this.handleCheckboxChange(checkbox);
          }
        }
      }
    });
  }

  initKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      if (e.ctrlKey || e.metaKey) {
        switch(e.key.toLowerCase()) {
          case 'a':
            e.preventDefault();
            this.selectAll();
            break;
          case 'd':
            e.preventDefault();
            this.selectNone();
            break;
          case 'i':
            e.preventDefault();
            this.selectReverse();
            break;
        }
      }
    });
  }

  performSearch(keyword) {
    keyword = keyword.toLowerCase().trim();
    const items = this.qualificationList.querySelectorAll('.qualification-item');

    items.forEach(item => {
      const name = item.dataset.name.toLowerCase();
      const description = item.dataset.description.toLowerCase();
      const isMatch = keyword === '' ||
                     name.includes(keyword) ||
                     description.includes(keyword);

      item.style.display = isMatch ? 'block' : 'none';

      // 高亮搜索关键词
      if (keyword && isMatch) {
        this.highlightKeyword(item, keyword);
      } else {
        this.removeHighlight(item);
      }
    });

    this.updateCounts();
  }

  highlightKeyword(item, keyword) {
    const nameEl = item.querySelector('.item-name');
    const descEl = item.querySelector('.item-description');

    [nameEl, descEl].forEach(el => {
      if (el) {
        const text = el.textContent;
        const regex = new RegExp(`(${keyword})`, 'gi');
        el.innerHTML = text.replace(regex, '<mark style="background: #fff3cd; padding: 1px 2px; border-radius: 2px;">$1</mark>');
      }
    });
  }

  removeHighlight(item) {
    const nameEl = item.querySelector('.item-name');
    const descEl = item.querySelector('.item-description');

    [nameEl, descEl].forEach(el => {
      if (el) {
        el.innerHTML = el.textContent;
      }
    });
  }

  applyFilters() {
    const validityFilter = this.validityFilter.value;
    const categoryFilter = this.categoryFilter.value;
    const items = this.qualificationList.querySelectorAll('.qualification-item');

    items.forEach(item => {
      let show = true;

      // 有效期筛选
      if (validityFilter) {
        const validDays = parseInt(item.dataset.validDays);
        if (validityFilter === 'permanent' && validDays !== 0) show = false;
        if (validityFilter === 'temporary' && validDays === 0) show = false;
      }

      // 分类筛选
      if (categoryFilter && item.dataset.category !== categoryFilter) {
        show = false;
      }

      item.style.display = show ? 'block' : 'none';
    });

    this.updateCounts();
  }

  handleCheckboxChange(checkbox) {
    const item = checkbox.closest('.qualification-item');
    this.updateItemStyle(item);
    this.updateSelectedDisplay();
    this.updateCounts();
    this.saveState();

    // 添加动画效果
    if (checkbox.checked) {
      item.style.animation = 'none';
      setTimeout(() => {
        item.style.animation = 'slideIn 0.3s ease-out';
      }, 10);
    }
  }

  updateItemStyle(item) {
    const checkbox = item.querySelector('input[type="checkbox"]');
    if (checkbox.checked) {
      item.classList.add('selected');
    } else {
      item.classList.remove('selected');
    }
  }

  updateAllItemStyles() {
    const items = this.qualificationList.querySelectorAll('.qualification-item');
    items.forEach(item => this.updateItemStyle(item));
  }

  updateSelectedDisplay() {
    const selectedCheckboxes = this.qualificationList.querySelectorAll('input[type="checkbox"]:checked');
    const count = selectedCheckboxes.length;

    // 更新计数
    this.selectedCount.textContent = count;

    // 显示/隐藏清空按钮
    this.clearAllBtn.style.display = count > 0 ? 'inline-flex' : 'none';

    // 生成标签
    let tagsHtml = '';
    if (count > 0) {
      selectedCheckboxes.forEach(checkbox => {
        const item = checkbox.closest('.qualification-item');
        const name = item.dataset.name;
        const validDays = parseInt(item.dataset.validDays);
        const validText = validDays === 0 ? '永久有效' : `${validDays}天`;

        tagsHtml += `
          <div class="selected-tag" data-id="${checkbox.value}">
            <i class="layui-icon layui-icon-ok"></i>
            <span>${name} (${validText})</span>
            <div class="tag-remove" onclick="qualificationSelector.removeSelected('${checkbox.value}')">
              <i class="layui-icon layui-icon-close"></i>
            </div>
          </div>
        `;
      });
    } else {
      tagsHtml = `
        <div class="empty-state">
          <i class="layui-icon layui-icon-tips"></i>
          暂未选择任何资质，请在下方列表中选择
        </div>
      `;
    }

    this.selectedTags.innerHTML = tagsHtml;
  }

  updateCounts() {
    const allItems = this.qualificationList.querySelectorAll('.qualification-item');
    const visibleItems = this.qualificationList.querySelectorAll('.qualification-item[style*="block"], .qualification-item:not([style*="none"])');
    const selectedItems = this.qualificationList.querySelectorAll('input[type="checkbox"]:checked');

    this.totalCount.textContent = allItems.length;
    this.visibleCount.textContent = visibleItems.length;
    this.selectedCount.textContent = selectedItems.length;
  }

  selectAll() {
    const visibleCheckboxes = this.qualificationList.querySelectorAll('.qualification-item:not([style*="none"]) input[type="checkbox"]');
    visibleCheckboxes.forEach(checkbox => {
      if (!checkbox.checked) {
        checkbox.checked = true;
        this.handleCheckboxChange(checkbox);
      }
    });

    // 显示提示
    if (visibleCheckboxes.length > 0) {
      this.showToast(`已选择 ${visibleCheckboxes.length} 个资质`, 'success');
    }
  }

  selectNone() {
    const checkboxes = this.qualificationList.querySelectorAll('input[type="checkbox"]:checked');
    const count = checkboxes.length;

    checkboxes.forEach(checkbox => {
      checkbox.checked = false;
      this.handleCheckboxChange(checkbox);
    });

    if (count > 0) {
      this.showToast(`已清空 ${count} 个资质`, 'info');
    }
  }

  selectReverse() {
    const visibleCheckboxes = this.qualificationList.querySelectorAll('.qualification-item:not([style*="none"]) input[type="checkbox"]');
    let changedCount = 0;

    visibleCheckboxes.forEach(checkbox => {
      checkbox.checked = !checkbox.checked;
      this.handleCheckboxChange(checkbox);
      changedCount++;
    });

    if (changedCount > 0) {
      this.showToast(`已反选 ${changedCount} 个资质`, 'info');
    }
  }

  removeSelected(id) {
    const checkbox = this.qualificationList.querySelector(`input[value="${id}"]`);
    if (checkbox) {
      checkbox.checked = false;
      this.handleCheckboxChange(checkbox);
    }
  }

  showQuickTemplates() {
    const templates = [
      { name: '基础营业资质', filter: { category: 'business', validity: 'permanent' } },
      { name: '技术认证资质', filter: { category: 'technical' } },
      { name: '安全相关资质', filter: { category: 'safety' } },
      { name: '质量体系资质', filter: { category: 'quality' } }
    ];

    let html = '<div style="padding: 20px;">';
    html += '<h3 style="margin-bottom: 16px; color: var(--text-primary);">快速选择模板</h3>';

    templates.forEach((template, index) => {
      html += `
        <div style="margin-bottom: 12px;">
          <button type="button" class="layui-btn layui-btn-normal layui-btn-sm"
                  onclick="qualificationSelector.applyTemplate('${template.filter.category}', '${template.filter.validity || ''}')">
            ${template.name}
          </button>
        </div>
      `;
    });

    html += '</div>';

    layer.open({
      type: 1,
      title: '快速选择模板',
      content: html,
      area: ['400px', '300px'],
      btn: ['关闭'],
      btnAlign: 'c'
    });
  }

  applyTemplate(category, validity) {
    // 先清空当前选择
    this.selectNone();

    // 根据模板筛选并选择
    const items = this.qualificationList.querySelectorAll('.qualification-item');
    let selectedCount = 0;

    items.forEach(item => {
      const itemCategory = item.dataset.category;
      const itemValidDays = parseInt(item.dataset.validDays);

      let shouldSelect = itemCategory === category;

      if (validity === 'permanent') {
        shouldSelect = shouldSelect && itemValidDays === 0;
      } else if (validity === 'temporary') {
        shouldSelect = shouldSelect && itemValidDays > 0;
      }

      if (shouldSelect) {
        const checkbox = item.querySelector('input[type="checkbox"]');
        if (checkbox) {
          checkbox.checked = true;
          this.handleCheckboxChange(checkbox);
          selectedCount++;
        }
      }
    });

    layer.closeAll();
    this.showToast(`已应用模板，选择了 ${selectedCount} 个资质`, 'success');
  }

  showToast(message, type = 'info') {
    const icons = {
      success: 1,
      error: 2,
      info: 0,
      warning: 3
    };

    layer.msg(message, {
      icon: icons[type] || 0,
      time: 2000
    });
  }

  saveState() {
    const selectedIds = Array.from(this.qualificationList.querySelectorAll('input[type="checkbox"]:checked'))
                           .map(cb => cb.value);
    localStorage.setItem('qualification_selector_state', JSON.stringify({
      selectedIds,
      timestamp: Date.now()
    }));
  }

  loadSavedState() {
    try {
      const saved = localStorage.getItem('qualification_selector_state');
      if (saved) {
        const state = JSON.parse(saved);
        // 只加载1小时内的状态
        if (Date.now() - state.timestamp < 3600000) {
          state.selectedIds.forEach(id => {
            const checkbox = this.qualificationList.querySelector(`input[value="${id}"]`);
            if (checkbox) {
              checkbox.checked = true;
              this.handleCheckboxChange(checkbox);
            }
          });
        }
      }
    } catch (e) {
      console.warn('Failed to load saved state:', e);
    }
  }
}

// 全局函数（保持向后兼容）
let qualificationSelector;

function selectAll() {
  qualificationSelector.selectAll();
}

function selectNone() {
  qualificationSelector.selectNone();
}

function selectReverse() {
  qualificationSelector.selectReverse();
}

function showQuickTemplates() {
  qualificationSelector.showQuickTemplates();
}

// 初始化
document.addEventListener('DOMContentLoaded', function() {
  qualificationSelector = new QualificationSelector();

  // 通知父页面重新渲染表单
  setTimeout(function() {
    if (window.layui && layui.form) {
      layui.form.render();
    }

    // 触发自定义事件，通知组件已初始化完成
    const event = new CustomEvent('qualificationSelectorReady');
    document.dispatchEvent(event);
  }, 300);
});
</script>
