-- 为商家资质表添加商品ID字段
-- 执行时间：2025-01-11

-- 添加商品ID字段
ALTER TABLE `ls_shop_qualification` 
ADD COLUMN `goods_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '商品ID，0表示通用资质，非0表示商品专用资质' 
AFTER `qualification_name`;

-- 添加索引以提高查询性能
ALTER TABLE `ls_shop_qualification` 
ADD INDEX `idx_goods_id` (`goods_id`);

-- 修改唯一索引，包含goods_id字段
ALTER TABLE `ls_shop_qualification` 
DROP INDEX `uk_shop_qualification`;

ALTER TABLE `ls_shop_qualification` 
ADD UNIQUE KEY `uk_shop_qualification_goods` (`shop_id`, `qualification_id`, `goods_id`, `del`);

-- 更新说明
-- goods_id: 0-通用资质（适用于所有商品），非0-商品专用资质（仅适用于指定商品）
-- 这样设计可以支持：
-- 1. 商家上传通用资质，适用于所有需要该资质的商品
-- 2. 商家为特定商品上传专用资质，仅适用于该商品
-- 3. 管理员可以分别审核通用资质和商品专用资质
