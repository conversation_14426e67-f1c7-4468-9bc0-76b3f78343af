<div class="layui-tab-item goods-content layui-show">
    <div class="layui-card-body" pad15>
        <div>
            <!-- 商品类型 -->
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="form-label-asterisk">*</span>商品类型：</label>
                <div class="layui-input-block">
                    <input type="radio" name="type" value="0" title="实物商品" class="layui-input" />
                    <!-- <input type="radio" name="type" value="1" title="虚拟商品" class="layui-input" /> -->
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"></label>
                <span style="color: #a3a3a3;font-size: 9px">选择好商品类型之后，编辑时不能修改类型。请谨慎选择</span>
            </div>
            <!--商品名称-->
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="form-label-asterisk">*</span>商品名称：</label>
                <div class="layui-input-block">
                    <input name="goods_id" type="hidden">
                    <input type="text" name="name" lay-verify="custom_required" lay-verType="tips" autocomplete="off"
                        maxlength="64" switch-tab="0" verify-msg="请输入商品名称，最多64个字符" placeholder="请输入商品名称，最少3个字符，最多64个字符"
                        class="layui-input">
                </div>
            </div>
            <!--商品编码-->
            <div class="layui-form-item">
                <label class="layui-form-label">商品编码：</label>
                <div class="layui-input-block">
                    <input type="text" name="code" lay-verType="tips" autocomplete="off" switch-tab="0"
                        class="layui-input">
                </div>
            </div>
            <!--平台分类-->
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="form-label-asterisk">*</span>平台分类：</label>
                <div class="layui-input-inline">
                    <select name="first_cate_id" lay-filter="first_category" lay-verify="custom_required"
                        lay-verType="tips" switch-tab="0" verify-msg="请选择分类">
                        <option value="">请选择分类</option>
                    </select>
                </div>
                <div class="layui-input-inline">
                    <select name="second_cate_id" lay-filter="second_category" switch-tab="0" verify-msg="请选择分类">
                        <option value="">请选择分类</option>
                    </select>
                </div>
                <div class="layui-input-inline">
                    <select name="third_cate_id" lay-filter="third_category" switch-tab="0" verify-msg="请选择分类">
                        <option value="">请选择分类</option>
                    </select>
                </div>
            </div>

            <!-- 资质审核区域 -->
            <div class="layui-form-item" id="qualification-audit-area" style="display: none;">
                <label class="layui-form-label">资质审核：</label>
                <div class="layui-input-block">
                    <div class="qualification-audit-container">
                        <div class="qualification-audit-header">
                            <i class="layui-icon layui-icon-vercode" style="color: #1890ff; margin-right: 8px;"></i>
                            <span style="font-weight: bold; color: #333;">平台分类所需资质</span>
                            <span style="color: #666; font-size: 12px; margin-left: 10px;">请审核商家上传的资质文件</span>
                        </div>
                        <div id="qualification-audit-list"></div>
                    </div>
                </div>
            </div>

            <!-- 资质审核样式 -->
            <style>
            .qualification-audit-container {
                border: 1px solid #d9d9d9;
                border-radius: 6px;
                background: #fafafa;
                padding: 16px;
                margin-bottom: 10px;
            }

            .qualification-audit-header {
                display: flex;
                align-items: center;
                margin-bottom: 16px;
                padding-bottom: 12px;
                border-bottom: 1px solid #e8e8e8;
            }

            .qualification-audit-item {
                margin-bottom: 20px;
                padding: 16px;
                border: 1px solid #e8e8e8;
                border-radius: 6px;
                background: #fff;
                position: relative;
            }

            .qualification-audit-item.required {
                border-color: #ff4d4f;
                background: #fff2f0;
            }

            .qualification-audit-item.optional {
                border-color: #faad14;
                background: #fffbe6;
            }

            .qualification-audit-item.approved {
                border-color: #52c41a;
                background: #f6ffed;
            }

            .qualification-audit-item.rejected {
                border-color: #ff4d4f;
                background: #fff2f0;
            }

            .qualification-item-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 12px;
            }

            .qualification-item-title {
                display: flex;
                align-items: center;
                font-weight: bold;
                font-size: 14px;
            }

            .qualification-badge {
                padding: 2px 8px;
                border-radius: 3px;
                font-size: 11px;
                color: white;
                margin-left: 8px;
            }

            .qualification-badge.required {
                background: #ff4d4f;
            }

            .qualification-badge.optional {
                background: #faad14;
            }

            .qualification-status {
                display: flex;
                align-items: center;
                font-size: 12px;
            }

            .qualification-status.approved {
                color: #52c41a;
            }

            .qualification-status.rejected {
                color: #ff4d4f;
            }

            .qualification-status.pending {
                color: #faad14;
            }

            .qualification-description {
                color: #666;
                font-size: 12px;
                line-height: 1.5;
                margin-bottom: 12px;
            }

            .qualification-document {
                margin-bottom: 12px;
            }

            .qualification-document-item {
                display: flex;
                align-items: center;
                padding: 8px 12px;
                background: #f5f5f5;
                border: 1px solid #d9d9d9;
                border-radius: 4px;
                margin-bottom: 8px;
            }

            .qualification-document-preview {
                width: 10%;
                height: 10%;
                border-radius: 4px;
                object-fit: cover;
                margin-right: 12px;
                cursor: pointer;
                border: 1px solid #d9d9d9;
            }

            .qualification-document-info {
                flex: 1;
                min-width: 0;
            }

            .qualification-document-name {
                font-size: 13px;
                color: #333;
                margin-bottom: 4px;
                word-break: break-all;
            }

            .qualification-document-meta {
                font-size: 11px;
                color: #999;
            }

            .qualification-audit-actions {
                display: flex;
                gap: 8px;
                align-items: center;
            }

            .qualification-audit-btn {
                padding: 6px 16px;
                border-radius: 4px;
                font-size: 12px;
                border: none;
                cursor: pointer;
                transition: all 0.3s;
                display: flex;
                align-items: center;
                gap: 4px;
            }

            .qualification-audit-btn.approve {
                background: #52c41a;
                color: white;
            }

            .qualification-audit-btn.approve:hover {
                background: #73d13d;
            }

            .qualification-audit-btn.reject {
                background: #ff4d4f;
                color: white;
            }

            .qualification-audit-btn.reject:hover {
                background: #ff7875;
            }

            .qualification-audit-btn:disabled {
                background: #f5f5f5;
                color: #bfbfbf;
                cursor: not-allowed;
            }

            .qualification-no-document {
                text-align: center;
                padding: 20px;
                color: #999;
                font-size: 12px;
                background: #f9f9f9;
                border: 1px dashed #d9d9d9;
                border-radius: 4px;
            }

            .qualification-audit-result {
                padding: 12px 16px;
                border-radius: 6px;
                font-size: 13px;
                font-weight: bold;
                display: flex;
                align-items: center;
                gap: 8px;
                margin-top: 12px;
            }

            .qualification-audit-result.approved {
                background: #f6ffed;
                color: #52c41a;
                border: 1px solid #b7eb8f;
            }

            .qualification-audit-result.not-uploaded {
                background: #fff2f0;
                color: #ff4d4f;
                border: 1px solid #ffccc7;
            }

            .qualification-audit-result.rejected {
                background: #fff2f0;
                color: #ff4d4f;
                border: 1px solid #ffccc7;
            }

            .qualification-audit-result.rejected i {
                color: #ff4d4f;
            }

            .qualification-audit-result.approved i {
                color: #52c41a;
            }

            .qualification-audit-result.rejected {
                background: #fff2f0;
                color: #ff4d4f;
                border: 1px solid #ffccc7;
            }

            .audit-buttons-group {
                display: flex;
                gap: 8px;
                margin-bottom: 8px;
            }

            .audit-buttons-label {
                font-size: 12px;
                color: #666;
                text-align: center;
            }

            .audit-remark-display {
                background: #fff2f0;
                border: 1px solid #ffccc7;
                border-radius: 4px;
                padding: 12px;
                margin: 12px 0;
                font-size: 12px;
                color: #ff4d4f;
                line-height: 1.5;
            }

            .audit-remark-display strong {
                color: #cf1322;
                margin-right: 8px;
            }
            </style>

            <!--商家分类-->
            <!-- <div class="layui-form-item" >
                <label class="layui-form-label"><span class="form-label-asterisk">*</span>商家分类：</label>
                <div class="layui-input-inline">
                    <select name="shop_cate_id" lay-filter="shop_cate_id" lay-verify="custom_required"
                        lay-verType="tips" switch-tab="0" verify-msg="请选择分类">
                    </select>
                </div>
            </div> -->
            <!--商品卖点-->
            <div class="layui-form-item">
                <label class="layui-form-label">商品卖点：</label>
                <div class="layui-input-block">
                    <input type="text" maxlength="60" name="remark" autocomplete="off" class="layui-input">
                </div>
            </div>
            <!--商品单位-->
            <!-- <div class="layui-form-item">
                <label class="layui-form-label">商品单位：</label>
                <div class="layui-input-inline">
                    <select name="unit_id" switch-tab="0" verify-msg="请选择分类">
                        <option value="">请选择单位</option>
                    </select>
                </div>
            </div> -->
            <!--商品品牌-->
            <!-- <div class="layui-form-item">
                <label class="layui-form-label">商品品牌：</label>
                <div class="layui-input-inline">
                    <select name="brand_id" lay-verType="tips" switch-tab="0" verify-msg="请选择商品品牌">
                        <option value="">请选择品牌</option>
                    </select>
                </div>
            </div> -->
            <!--供货商-->
            <!-- <div class="layui-form-item">
                <label class="layui-form-label">供货商：</label>
                <div class="layui-input-inline">
                    <select name="supplier_id" lay-verType="tips" switch-tab="0" verify-msg="请选择供货商">
                        <option value="">请选择供货商</option>
                    </select>
                </div>
            </div> -->
            <!--商品主图-->
            <div class="layui-form-item" style="margin-bottom: 0px">
                <label class="layui-form-label"><span class="form-label-asterisk">*</span>商品封面图：</label>
                <div class="layui-input-block" id="imageContainer">
                    <div class="like-upload-image">
                        <div class="upload-image-elem"><a class="add-upload-image" id="image"> + 添加图片</a></div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"></label>
                <span style="color: #a3a3a3;font-size: 9px">建议尺寸：800*800像素</span>
            </div>
            <!--自定义分享海报-->
            <!-- <div class="layui-form-item" style="margin-bottom: 0px">
                <label class="layui-form-label">分享海报：</label>
                <div class="layui-input-block" id="posterContainer">
                    <div class="like-upload-image">
                        <div class="upload-image-elem"><a class="add-upload-image" id="poster"> + 添加图片</a></div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"></label>
                <span style="color: #a3a3a3;font-size: 9px">建议尺寸：800*800像素</span>
            </div> -->
            <!--商品轮播图-->
            <div class="layui-form-item" style="margin-bottom: 0px">
                <label class="layui-form-label"><span class="form-label-asterisk">*</span>商品轮播图：</label>
                <div class="layui-input-block" id="goodsImageContainer">
                    <div class="like-upload-image">
                        <div class="upload-image-elem"><a class="add-upload-image" id="goodsimage"> + 添加图片</a></div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"></label>
                <span style="color: #a3a3a3;font-size: 9px">建议尺寸：800*800像素，最多上传5张</span>
            </div>
            <!--商品视频-->
            <div class="layui-form-item">
                <label class="layui-form-label">商品视频：</label>
                <div class="layui-input-block" id="videoContainer">
                    <div class="like-upload-video">
                        <div class="upload-image-elem"><a class="add-upload-video" id="video"> + 添加视频</a></div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"></label>
                <span style="color: #a3a3a3;font-size: 9px">商品视频，在商品详情页面播放</span>
            </div>
        </div>
    </div>
</div>