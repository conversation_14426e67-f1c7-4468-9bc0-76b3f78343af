{layout name="layout2" /}

<style>
.upload-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.qualification-info {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #1890ff;
}

.goods-info {
    background: #e6f7ff;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
}

.upload-area {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    background: #fafafa;
    transition: all 0.3s;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #1890ff;
    background: #f0f9ff;
}

.upload-area.dragover {
    border-color: #1890ff;
    background: #e6f7ff;
}

.upload-icon {
    font-size: 48px;
    color: #d9d9d9;
    margin-bottom: 15px;
}

.upload-text {
    color: #666;
    margin-bottom: 10px;
}

.upload-hint {
    font-size: 12px;
    color: #999;
}

.preview-container {
    margin-top: 20px;
    display: none;
}

.preview-image {
    max-width: 100%;
    max-height: 300px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.form-actions {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e6e6e6;
}

.example-image {
    max-width: 200px;
    max-height: 150px;
    border-radius: 4px;
    cursor: pointer;
    transition: transform 0.2s;
}

.example-image:hover {
    transform: scale(1.05);
}
</style>

<div class="upload-container">
    <!-- 商品信息 -->
    {if $goods}
    <div class="goods-info">
        <h4 style="margin: 0 0 10px 0; color: #1890ff;">商品信息</h4>
        <div style="display: flex; align-items: center; gap: 15px;">
            <img src="{$goods.image|default='/static/common/image/default_goods.png'}" 
                 style="width: 50px; height: 50px; border-radius: 4px; object-fit: cover;">
            <div>
                <div style="font-weight: 600; margin-bottom: 5px;">{$goods.name}</div>
                <div style="font-size: 12px; color: #666;">商品ID: {$goods.id}</div>
            </div>
        </div>
    </div>
    {/if}

    <!-- 资质信息 -->
    <div class="qualification-info">
        <h4 style="margin: 0 0 15px 0; color: #333;">资质信息</h4>
        <div style="margin-bottom: 10px;">
            <strong>资质名称：</strong>{$qualification.name|default=$qualification_name}
        </div>
        {if $qualification.description}
        <div style="margin-bottom: 10px;">
            <strong>资质描述：</strong>{$qualification.description}
        </div>
        {/if}
        {if $qualification.ex_img}
        <div style="margin-bottom: 10px;">
            <strong>示例图片：</strong>
            <img src="{$qualification.ex_img}" class="example-image" onclick="showExample(this.src)" alt="示例图片">
        </div>
        {/if}
    </div>

    <!-- 上传表单 -->
    <form class="layui-form" lay-filter="upload-form">
        <input type="hidden" name="goods_id" value="{$goods_id}">
        <input type="hidden" name="qualification_id" value="{$qualification_id}">
        <input type="hidden" name="document_path" id="document_path">
        <input type="hidden" name="document_name" id="document_name">

        <div class="layui-form-item">
            <label class="layui-form-label">资质文件：</label>
            <div class="layui-input-block">
                <div class="upload-area" id="upload-area">
                    <div class="upload-icon">
                        <i class="layui-icon layui-icon-upload"></i>
                    </div>
                    <div class="upload-text">点击或拖拽文件到此处上传</div>
                    <div class="upload-hint">支持 JPG、PNG、PDF 格式，文件大小不超过 10MB</div>
                </div>
                <input type="file" id="file-input" accept=".jpg,.jpeg,.png,.pdf" style="display: none;">
                
                <div class="preview-container" id="preview-container">
                    <img id="preview-image" class="preview-image" alt="预览图片">
                </div>
            </div>
        </div>

        {if $qualification.valid_days > 0}
        <div class="layui-form-item">
            <label class="layui-form-label">有效期至：</label>
            <div class="layui-input-block">
                <input type="text" name="expire_time" id="expire_time" placeholder="请选择有效期" 
                       autocomplete="off" class="layui-input" lay-verify="required">
            </div>
        </div>
        {/if}

        <div class="form-actions">
            <button type="submit" class="layui-btn layui-btn-normal" lay-submit lay-filter="submit">
                <i class="layui-icon layui-icon-upload"></i>
                提交资质
            </button>
            <button type="button" class="layui-btn layui-btn-primary" onclick="parent.layer.closeAll()">
                取消
            </button>
        </div>
    </form>
</div>

<script>
layui.use(['form', 'laydate', 'upload', 'layer'], function() {
    var form = layui.form;
    var laydate = layui.laydate;
    var upload = layui.upload;
    var layer = layui.layer;

    // 日期选择器
    {if $qualification.valid_days > 0}
    laydate.render({
        elem: '#expire_time',
        type: 'datetime',
        min: 0, // 不能选择今天之前的日期
        done: function(value) {
            console.log('选择的日期：', value);
        }
    });
    {/if}

    // 文件上传
    var uploadInst = upload.render({
        elem: '#upload-area',
        url: '{:url("common.upload/image")}',
        accept: 'file',
        acceptMime: 'image/jpeg,image/jpg,image/png,application/pdf',
        size: 10240, // 10MB
        before: function(obj) {
            layer.load();
        },
        done: function(res) {
            layer.closeAll('loading');
            if (res.code === 1) {
                $('#document_path').val(res.data.url);
                $('#document_name').val(res.data.name);
                
                // 显示预览
                if (res.data.url.match(/\.(jpg|jpeg|png)$/i)) {
                    $('#preview-image').attr('src', res.data.url);
                    $('#preview-container').show();
                }
                
                layer.msg('上传成功', {icon: 1});
            } else {
                layer.msg(res.msg || '上传失败', {icon: 2});
            }
        },
        error: function() {
            layer.closeAll('loading');
            layer.msg('上传失败，请重试', {icon: 2});
        }
    });

    // 拖拽上传
    var uploadArea = document.getElementById('upload-area');
    var fileInput = document.getElementById('file-input');

    uploadArea.addEventListener('click', function() {
        fileInput.click();
    });

    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        
        var files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileUpload(files[0]);
        }
    });

    fileInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            handleFileUpload(e.target.files[0]);
        }
    });

    // 处理文件上传
    function handleFileUpload(file) {
        // 验证文件类型
        var allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
        if (allowedTypes.indexOf(file.type) === -1) {
            layer.msg('文件格式不支持，请上传 JPG、PNG 或 PDF 格式的文件', {icon: 2});
            return;
        }

        // 验证文件大小
        if (file.size > 10 * 1024 * 1024) {
            layer.msg('文件大小不能超过 10MB', {icon: 2});
            return;
        }

        // 创建 FormData 对象
        var formData = new FormData();
        formData.append('file', file);

        // 上传文件
        layer.load();
        $.ajax({
            url: '{:url("common.upload/image")}',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(res) {
                layer.closeAll('loading');
                if (res.code === 1) {
                    $('#document_path').val(res.data.url);
                    $('#document_name').val(res.data.name);
                    
                    // 显示预览
                    if (file.type.indexOf('image') === 0) {
                        $('#preview-image').attr('src', res.data.url);
                        $('#preview-container').show();
                    }
                    
                    layer.msg('上传成功', {icon: 1});
                } else {
                    layer.msg(res.msg || '上传失败', {icon: 2});
                }
            },
            error: function() {
                layer.closeAll('loading');
                layer.msg('上传失败，请重试', {icon: 2});
            }
        });
    }

    // 表单提交
    form.on('submit(submit)', function(data) {
        if (!$('#document_path').val()) {
            layer.msg('请先上传资质文件', {icon: 2});
            return false;
        }

        $.ajax({
            url: '{:url("upload")}',
            type: 'POST',
            data: data.field,
            success: function(res) {
                if (res.code === 1) {
                    layer.msg('提交成功', {icon: 1}, function() {
                        parent.layer.closeAll();
                    });
                } else {
                    layer.msg(res.msg || '提交失败', {icon: 2});
                }
            },
            error: function() {
                layer.msg('网络错误，请重试', {icon: 2});
            }
        });

        return false;
    });

    // 显示示例图片
    window.showExample = function(src) {
        layer.photos({
            photos: {
                title: '资质示例图片',
                data: [{
                    alt: '资质示例',
                    src: src
                }]
            }
        });
    };
});
</script>
