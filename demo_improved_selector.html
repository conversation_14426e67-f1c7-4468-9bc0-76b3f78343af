<!DOCTYPE html>
<html>
<head>
    <title>改进的资质选择器演示</title>
    <link rel="stylesheet" href="https://unpkg.com/layui@2.8.18/dist/css/layui.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            margin: 20px; 
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        .demo-container { 
            max-width: 900px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 12px; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .qualification-item:hover { 
            transform: translateY(-2px); 
            box-shadow: 0 4px 12px rgba(24,144,255,0.2) !important;
        }
        .selected-tag:hover { 
            transform: scale(1.05) !important; 
        }
        .layui-input:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 style="text-align: center; color: #333; margin-bottom: 10px; font-size: 28px;">资质选择器 2.0</h1>
        <p style="text-align: center; color: #666; margin-bottom: 40px;">更美观、更便捷的资质选择体验</p>
        
        <div class="layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label" style="color: #6a6f6c; width: 140px;">
                    <span style="color: red;">*</span>选择资质：
                </label>
                <div class="layui-input-block" style="margin-left: 170px;">
                    <!-- 搜索和操作栏 -->
                    <div style="margin-bottom: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                        <div style="display: flex; align-items: center; gap: 10px; flex-wrap: wrap;">
                            <input type="text" id="qualification-search" placeholder="输入关键词搜索资质..." class="layui-input" style="flex: 1; min-width: 200px; max-width: 300px;">
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" onclick="selectAll()">
                                <i class="layui-icon layui-icon-ok"></i> 全选
                            </button>
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" onclick="selectNone()">
                                <i class="layui-icon layui-icon-close"></i> 清空
                            </button>
                            <span style="color: #666; font-size: 14px; font-weight: bold;">
                                已选: <span id="selected-count" style="color: #1890ff;">0</span> 个
                            </span>
                        </div>
                    </div>

                    <!-- 已选择的资质标签 -->
                    <div id="selected-tags-container" style="margin-bottom: 15px; min-height: 40px; padding: 10px; background: #fff; border: 1px solid #d9d9d9; border-radius: 4px;">
                        <div style="color: #666; font-size: 12px; margin-bottom: 8px;">已选择的资质：</div>
                        <div id="selected-tags">
                            <span style="color: #999; font-style: italic;">暂未选择任何资质</span>
                        </div>
                    </div>

                    <!-- 资质选择列表 -->
                    <div style="border: 1px solid #d9d9d9; border-radius: 4px; background: #fff;">
                        <div style="padding: 10px; border-bottom: 1px solid #f0f0f0; background: #fafafa;">
                            <span style="color: #666; font-size: 12px;">可选择的资质 (点击选择)：</span>
                        </div>
                        <div id="qualification-list" style="max-height: 300px; overflow-y: auto; padding: 10px;">
                            <!-- 模拟资质数据 -->
                            <label class="qualification-item" data-id="1" data-name="食品经营许可证" data-description="食品经营相关资质证明" data-valid-days="365" style="display: block; margin: 8px 0; padding: 12px; border: 2px solid #f0f0f0; border-radius: 6px; cursor: pointer; transition: all 0.2s; background: #fff;">
                                <div style="display: flex; align-items: center;">
                                    <input type="checkbox" name="qualification_ids[]" value="1" style="margin-right: 12px; transform: scale(1.2);">
                                    <div style="flex: 1;">
                                        <div style="font-weight: 600; color: #333; font-size: 14px; margin-bottom: 4px;">食品经营许可证</div>
                                        <div style="font-size: 12px; color: #666; line-height: 1.4;">
                                            <span>食品经营相关资质证明</span>
                                            <span style="margin-left: 8px; padding: 2px 6px; background: #e6f7ff; color: #1890ff; border-radius: 10px; font-size: 11px;">365天有效</span>
                                        </div>
                                    </div>
                                </div>
                            </label>
                            
                            <label class="qualification-item" data-id="2" data-name="医疗器械经营许可证" data-description="医疗器械经营资质" data-valid-days="0" style="display: block; margin: 8px 0; padding: 12px; border: 2px solid #f0f0f0; border-radius: 6px; cursor: pointer; transition: all 0.2s; background: #fff;">
                                <div style="display: flex; align-items: center;">
                                    <input type="checkbox" name="qualification_ids[]" value="2" style="margin-right: 12px; transform: scale(1.2);">
                                    <div style="flex: 1;">
                                        <div style="font-weight: 600; color: #333; font-size: 14px; margin-bottom: 4px;">医疗器械经营许可证</div>
                                        <div style="font-size: 12px; color: #666; line-height: 1.4;">
                                            <span>医疗器械经营资质</span>
                                            <span style="margin-left: 8px; padding: 2px 6px; background: #e6f7ff; color: #1890ff; border-radius: 10px; font-size: 11px;">永久有效</span>
                                        </div>
                                    </div>
                                </div>
                            </label>
                            
                            <label class="qualification-item" data-id="3" data-name="化妆品生产许可证" data-description="化妆品生产相关证明" data-valid-days="730" style="display: block; margin: 8px 0; padding: 12px; border: 2px solid #f0f0f0; border-radius: 6px; cursor: pointer; transition: all 0.2s; background: #fff;">
                                <div style="display: flex; align-items: center;">
                                    <input type="checkbox" name="qualification_ids[]" value="3" style="margin-right: 12px; transform: scale(1.2);">
                                    <div style="flex: 1;">
                                        <div style="font-weight: 600; color: #333; font-size: 14px; margin-bottom: 4px;">化妆品生产许可证</div>
                                        <div style="font-size: 12px; color: #666; line-height: 1.4;">
                                            <span>化妆品生产相关证明</span>
                                            <span style="margin-left: 8px; padding: 2px 6px; background: #e6f7ff; color: #1890ff; border-radius: 10px; font-size: 11px;">730天有效</span>
                                        </div>
                                    </div>
                                </div>
                            </label>
                            
                            <label class="qualification-item" data-id="4" data-name="CCC安全认证证书" data-description="产品安全认证" data-valid-days="1095" style="display: block; margin: 8px 0; padding: 12px; border: 2px solid #f0f0f0; border-radius: 6px; cursor: pointer; transition: all 0.2s; background: #fff;">
                                <div style="display: flex; align-items: center;">
                                    <input type="checkbox" name="qualification_ids[]" value="4" style="margin-right: 12px; transform: scale(1.2);">
                                    <div style="flex: 1;">
                                        <div style="font-weight: 600; color: #333; font-size: 14px; margin-bottom: 4px;">CCC安全认证证书</div>
                                        <div style="font-size: 12px; color: #666; line-height: 1.4;">
                                            <span>产品安全认证</span>
                                            <span style="margin-left: 8px; padding: 2px 6px; background: #e6f7ff; color: #1890ff; border-radius: 10px; font-size: 11px;">1095天有效</span>
                                        </div>
                                    </div>
                                </div>
                            </label>
                            
                            <label class="qualification-item" data-id="5" data-name="营业执照" data-description="企业营业执照" data-valid-days="0" style="display: block; margin: 8px 0; padding: 12px; border: 2px solid #f0f0f0; border-radius: 6px; cursor: pointer; transition: all 0.2s; background: #fff;">
                                <div style="display: flex; align-items: center;">
                                    <input type="checkbox" name="qualification_ids[]" value="5" style="margin-right: 12px; transform: scale(1.2);">
                                    <div style="flex: 1;">
                                        <div style="font-weight: 600; color: #333; font-size: 14px; margin-bottom: 4px;">营业执照</div>
                                        <div style="font-size: 12px; color: #666; line-height: 1.4;">
                                            <span>企业营业执照</span>
                                            <span style="margin-left: 8px; padding: 2px 6px; background: #e6f7ff; color: #1890ff; border-radius: 10px; font-size: 11px;">永久有效</span>
                                        </div>
                                    </div>
                                </div>
                            </label>

                            <label class="qualification-item" data-id="6" data-name="税务登记证" data-description="税务相关证明" data-valid-days="365" style="display: block; margin: 8px 0; padding: 12px; border: 2px solid #f0f0f0; border-radius: 6px; cursor: pointer; transition: all 0.2s; background: #fff;">
                                <div style="display: flex; align-items: center;">
                                    <input type="checkbox" name="qualification_ids[]" value="6" style="margin-right: 12px; transform: scale(1.2);">
                                    <div style="flex: 1;">
                                        <div style="font-weight: 600; color: #333; font-size: 14px; margin-bottom: 4px;">税务登记证</div>
                                        <div style="font-size: 12px; color: #666; line-height: 1.4;">
                                            <span>税务相关证明</span>
                                            <span style="margin-left: 8px; padding: 2px 6px; background: #e6f7ff; color: #1890ff; border-radius: 10px; font-size: 11px;">365天有效</span>
                                        </div>
                                    </div>
                                </div>
                            </label>
                        </div>
                    </div>
                    <div class="layui-form-mid layui-word-aux" style="margin-top: 10px;">请至少选择一个资质证书</div>
                </div>
            </div>
            
            <div class="layui-form-item" style="margin-top: 30px;">
                <div class="layui-input-block" style="margin-left: 170px;">
                    <button type="button" class="layui-btn layui-btn-normal" onclick="getSelectedValues()">
                        <i class="layui-icon layui-icon-ok"></i> 获取选中值
                    </button>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="resetSelection()">
                        <i class="layui-icon layui-icon-refresh"></i> 重置
                    </button>
                </div>
            </div>
        </div>
        
        <div id="result" style="margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 8px; display: none;">
            <h3 style="color: #333; margin-bottom: 15px;">选中的资质：</h3>
            <pre id="result-content" style="background: #fff; padding: 15px; border-radius: 4px; border: 1px solid #e6e6e6;"></pre>
        </div>
    </div>

    <script>
        // 初始化资质选择器
        $(document).ready(function() {
            initQualificationSelector();
        });

        function initQualificationSelector() {
            // 初始化样式和显示
            updateAllItemStyles();
            updateSelectedDisplay();

            // 搜索功能
            $('#qualification-search').on('input', function () {
                var keyword = $(this).val().toLowerCase().trim();
                $('.qualification-item').each(function () {
                    var name = $(this).data('name').toString().toLowerCase();
                    var description = $(this).data('description').toString().toLowerCase();
                    if (keyword === '' || name.indexOf(keyword) !== -1 || description.indexOf(keyword) !== -1) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            });

            // 监听复选框变化
            $(document).on('change', 'input[name="qualification_ids[]"]', function () {
                updateItemStyle($(this).closest('.qualification-item'));
                updateSelectedDisplay();
            });

            // 点击标签项也能选择
            $(document).on('click', '.qualification-item', function (e) {
                if (e.target.type !== 'checkbox') {
                    var checkbox = $(this).find('input[type="checkbox"]');
                    checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
                }
            });
        }

        // 更新单个项目样式
        function updateItemStyle(item) {
            var checkbox = item.find('input[type="checkbox"]');
            if (checkbox.prop('checked')) {
                item.css({
                    'background': 'linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%)',
                    'border-color': '#1890ff',
                    'box-shadow': '0 2px 8px rgba(24,144,255,0.15)'
                });
            } else {
                item.css({
                    'background': '#fff',
                    'border-color': '#f0f0f0',
                    'box-shadow': 'none'
                });
            }
        }

        // 更新所有项目样式
        function updateAllItemStyles() {
            $('.qualification-item').each(function () {
                updateItemStyle($(this));
            });
        }

        // 更新已选择显示
        function updateSelectedDisplay() {
            var selectedItems = $('input[name="qualification_ids[]"]:checked');
            var count = selectedItems.length;
            $('#selected-count').text(count);

            var tagsHtml = '';
            if (count > 0) {
                selectedItems.each(function () {
                    var item = $(this).closest('.qualification-item');
                    var name = item.data('name');
                    var validDays = item.data('valid-days');
                    var validText = validDays == 0 ? '永久有效' : validDays + '天';

                    tagsHtml += '<span class="selected-tag" data-id="' + $(this).val() + '" style="display: inline-block; margin: 3px; padding: 6px 10px; background: linear-gradient(135deg, #1890ff, #40a9ff); color: white; border-radius: 16px; font-size: 12px; cursor: pointer; box-shadow: 0 2px 4px rgba(24,144,255,0.3); transition: all 0.2s;" onmouseover="this.style.transform=\'scale(1.05)\'" onmouseout="this.style.transform=\'scale(1)\'">' +
                        '<i class="layui-icon layui-icon-ok" style="margin-right: 4px; font-size: 10px;"></i>' +
                        name + ' (' + validText + ')' +
                        '<i class="layui-icon layui-icon-close" style="margin-left: 6px; font-size: 10px; opacity: 0.8;" onclick="removeSelected(' + $(this).val() + ')"></i>' +
                        '</span>';
                });
            } else {
                tagsHtml = '<span style="color: #999; font-style: italic; padding: 8px;">暂未选择任何资质，请在下方列表中选择</span>';
            }

            $('#selected-tags').html(tagsHtml);
            updateAllItemStyles();
        }

        // 全选
        function selectAll() {
            $('.qualification-item:visible input[type="checkbox"]').prop('checked', true);
            updateSelectedDisplay();
        }

        // 全不选
        function selectNone() {
            $('input[name="qualification_ids[]"]').prop('checked', false);
            updateSelectedDisplay();
        }

        // 移除已选择的资质
        function removeSelected(id) {
            $('input[name="qualification_ids[]"][value="' + id + '"]').prop('checked', false);
            updateSelectedDisplay();
        }

        // 获取选中值
        function getSelectedValues() {
            var selectedValues = [];
            $('input[name="qualification_ids[]"]:checked').each(function() {
                var item = $(this).closest('.qualification-item');
                selectedValues.push({
                    id: $(this).val(),
                    name: item.data('name'),
                    description: item.data('description'),
                    valid_days: item.data('valid-days')
                });
            });
            
            $('#result-content').text(JSON.stringify(selectedValues, null, 2));
            $('#result').show();
        }

        // 重置选择
        function resetSelection() {
            selectNone();
            $('#qualification-search').val('');
            $('.qualification-item').show();
            $('#result').hide();
        }
    </script>
</body>
</html>