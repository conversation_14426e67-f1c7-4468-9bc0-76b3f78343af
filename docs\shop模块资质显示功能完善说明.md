# Shop模块资质显示功能完善说明

## 概述
为了让shop模块中的商品添加页面(add.html)在原有资质检查功能基础上增加资质包的显示，让商家能够清楚地看到资质包的组织结构，对以下文件进行了修改：

## 修改的文件

### 1. app/shop/controller/goods/Goods.php
**新增方法：**
- `getQualificationAudit()` - 获取商品资质审核信息（商家端）

**功能说明：**
- 接收category_id和goods_id参数
- 验证商品权限（确保商品属于当前商家）
- 调用admin模块的GoodsLogic::getQualificationAudit方法获取完整的资质数据
- 返回与admin模块相同的数据结构

### 2. app/shop/view/goods/goods/add.html
**修改内容：**
- 更新`checkCategoryQualifications()`函数，同时调用资质检查和资质包获取两个接口
- 修改`showQualificationAlert()`函数，在原有资质显示基础上增加资质包信息显示
- 增加去重逻辑，避免资质包中的资质与独立资质重复显示

**功能特点：**
- 保持原有的资质检查和提醒功能不变
- 在原有显示区域增加资质包信息
- 显示资质包名称、选择模式（全部需要/任选其一）
- 显示资质包内每个资质的上传状态
- 避免重复显示已在资质包中的独立资质
- 保持原有的上传资质功能

## 功能流程

### 新增商品时：
1. 用户选择商品分类
2. 系统调用`checkCategoryQualifications()`
3. 同时调用资质检查接口和资质包获取接口
4. 在原有资质提醒区域显示资质包信息和独立资质
5. 用户可以直接上传缺失的资质

### 编辑商品时：
1. 页面加载时自动调用`checkCategoryQualifications()`
2. 显示商品当前的资质设置和上传状态
3. 根据商品的`use_custom_qualification`字段决定显示分类资质还是独立资质
4. 显示已上传资质的审核状态

## 显示内容

### 资质包信息（新增）：
- 资质包名称
- 选择模式（全部需要/任选其一）
- 资质包内每个资质的上传状态
- 蓝色边框区分资质包

### 独立资质信息（原有）：
- 缺失的必传资质
- 过期的必传资质
- 可选资质
- 排除已在资质包中显示的资质，避免重复

### 功能按钮（保持原有）：
- 上传资质按钮
- 示例图片查看按钮

## 与原有功能的关系

### 保持不变：
- 原有的资质检查逻辑
- 原有的上传资质功能
- 原有的示例图片查看功能
- 原有的表单提交验证

### 新增功能：
- 资质包信息显示
- 资质包内资质状态显示
- 去重逻辑避免重复显示

## 技术实现

### 数据获取：
- 复用admin模块的`GoodsLogic::getQualificationAudit()`方法
- 确保数据结构的一致性

### 权限控制：
- 验证商品所有权
- 只允许查看自己的商品资质信息

### 用户体验：
- 保持与现有功能的一致性
- 上传后自动刷新显示
- 错误提示清晰明确

## 测试建议

1. 测试新增商品时的资质显示
2. 测试编辑商品时的资质显示
3. 测试资质上传功能
4. 测试示例图片查看功能
5. 测试不同分类的资质要求显示
6. 测试商品独立资质设置的显示

## 注意事项

1. 确保admin模块的`GoodsLogic::getQualificationAudit()`方法正常工作
2. 确保资质上传功能正常
3. 确保样式在不同浏览器中显示正常
4. 确保JavaScript函数没有冲突
