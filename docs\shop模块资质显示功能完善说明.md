# Shop模块资质显示功能完善说明

## 概述
为了让shop模块中的商品添加页面(add.html)显示完整的资质包和资质信息，与admin模块保持一致，对以下文件进行了修改：

## 修改的文件

### 1. app/shop/controller/goods/Goods.php
**新增方法：**
- `getQualificationAudit()` - 获取商品资质审核信息（商家端）

**功能说明：**
- 接收category_id和goods_id参数
- 验证商品权限（确保商品属于当前商家）
- 调用admin模块的GoodsLogic::getQualificationAudit方法获取完整的资质数据
- 返回与admin模块相同的数据结构

### 2. app/shop/view/goods/goods/goods_base.html
**新增内容：**
- 完整资质显示区域HTML结构
- 资质包相关CSS样式

**具体修改：**
- 在原有的"资质提醒区域"后面添加了"完整资质显示区域"
- 添加了资质包容器、资质项目、文档预览等样式
- 样式与admin模块保持一致，但去掉了审核相关的样式

### 3. app/shop/view/goods/goods/add.html
**修改内容：**
- 更新`checkCategoryQualifications()`函数，同时调用完整显示功能
- 新增`loadQualificationAudit()`函数 - 加载资质审核信息
- 新增`showQualificationAudit()`函数 - 显示资质包和资质列表
- 新增`hideQualificationAudit()`函数 - 隐藏资质审核区域

**功能特点：**
- 显示资质包名称、选择模式（全部需要/任选其一）
- 显示每个资质的详细信息（名称、描述、状态、文档等）
- 支持查看示例图片
- 支持上传资质文档
- 上传完成后自动刷新显示
- 区分商品专用资质和通用资质

## 功能流程

### 新增商品时：
1. 用户选择商品分类
2. 系统调用`checkCategoryQualifications()`
3. 同时调用简单检查和完整显示两个功能
4. 显示该分类需要的所有资质包和资质
5. 用户可以直接上传缺失的资质

### 编辑商品时：
1. 页面加载时自动调用`checkCategoryQualifications()`
2. 显示商品当前的资质设置和上传状态
3. 根据商品的`use_custom_qualification`字段决定显示分类资质还是独立资质
4. 显示已上传资质的审核状态

## 显示内容

### 资质包信息：
- 资质包名称
- 选择模式（全部需要/任选其一）
- 选择模式说明

### 资质信息：
- 资质名称和描述
- 有效期信息
- 上传状态（未上传/待审核/审核通过/审核未通过）
- 已上传的文档预览
- 示例图片按钮
- 上传/重新上传按钮

## 与Admin模块的区别

### 相同点：
- 数据结构完全一致
- 显示样式基本相同
- 支持资质包和独立资质显示
- 支持示例图片查看

### 不同点：
- 去掉了审核按钮和审核操作
- 去掉了重审功能
- 专注于商家端的上传和查看功能
- 保留了上传资质的功能

## 技术实现

### 数据获取：
- 复用admin模块的`GoodsLogic::getQualificationAudit()`方法
- 确保数据结构的一致性

### 权限控制：
- 验证商品所有权
- 只允许查看自己的商品资质信息

### 用户体验：
- 保持与现有功能的一致性
- 上传后自动刷新显示
- 错误提示清晰明确

## 测试建议

1. 测试新增商品时的资质显示
2. 测试编辑商品时的资质显示
3. 测试资质上传功能
4. 测试示例图片查看功能
5. 测试不同分类的资质要求显示
6. 测试商品独立资质设置的显示

## 注意事项

1. 确保admin模块的`GoodsLogic::getQualificationAudit()`方法正常工作
2. 确保资质上传功能正常
3. 确保样式在不同浏览器中显示正常
4. 确保JavaScript函数没有冲突
