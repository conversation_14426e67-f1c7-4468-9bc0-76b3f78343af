{layout name="layout2" /}
<style>
  .layui-form-label {
    color: #6a6f6c;
    width: 140px;
  }

  .layui-input-block {
    margin-left: 170px;
  }

  .reqRed::before {
    content: '*';
    color: red;
  }
</style>
<div class="layui-form" style="padding: 20px 30px 0 0;">
  <input type="hidden" name="id" value="{$info.id}">
  <div class="layui-form-item">
    <label class="layui-form-label reqRed">资质包名称：</label>
    <div class="layui-input-block">
      <input type="text" name="name" value="{$info.name}" lay-verify="required" lay-verType="tips"
        placeholder="请输入资质包名称" autocomplete="off" class="layui-input">
    </div>
  </div>
  <div class="layui-form-item">
    <label class="layui-form-label">资质包描述：</label>
    <div class="layui-input-block">
      <textarea name="remark" placeholder="请输入资质包描述" class="layui-textarea">{$info.remark}</textarea>
    </div>
  </div>

  <div class="layui-form-item">
    <label class="layui-form-label reqRed">选择模式：</label>
    <div class="layui-input-block">
      <input type="radio" name="selection_mode" value="1" title="全部需要" {if $info.selection_mode.value=='1'}checked{/if}>
      <input type="radio" name="selection_mode" value="2" title="任选其一" {if $info.selection_mode.value=='2'}checked{/if}>
    </div>
  </div>

  <div class="layui-form-item">
    <label class="layui-form-label reqRed">选择资质：</label>
    <div class="layui-input-block">
      {include file="goods/package/qualification_selector" /}
    </div>
  </div>

  <div class="layui-form-item">
    <label class="layui-form-label">排序：</label>
    <div class="layui-input-inline">
      <input type="number" name="sort" value="{$info.sort}" min="0" class="layui-input">
    </div>
    <div class="layui-form-mid layui-word-aux">数值越小，排序越靠前</div>
  </div>
  <div class="layui-form-item">
    <label class="layui-form-label">状态：</label>
    <div class="layui-input-block">
      <input type="radio" name="status" value="1" title="启用" {if $info.status==1}checked{/if}>
      <input type="radio" name="status" value="0" title="禁用" {if $info.status==0}checked{/if}>
    </div>
  </div>
  <div class="layui-form-item layui-hide">
    <input type="button" lay-submit lay-filter="package-submit-edit" id="package-submit-edit" value="确认">
  </div>
</div>
<script>
  layui.use(['form', 'like'], function () {
    var form = layui.form;

    // 监听表单提交
    form.on('submit(package-submit-edit)', function (data) {
      // 验证是否选择了资质
      var checkedQualifications = $('input[name="qualification_ids[]"]:checked');
      if (checkedQualifications.length === 0) {
        layer.msg('请至少选择一个资质证书', { icon: 2 });
        return false;
      }
      return true;
    });

    // 确保在DOM完全加载后重新渲染表单
    setTimeout(function() {
      form.render();
    }, 100);
  });

  // 监听资质选择器初始化完成事件
  document.addEventListener('qualificationSelectorReady', function() {
    if (window.layui && layui.form) {
      layui.form.render();
    }
  });
</script>