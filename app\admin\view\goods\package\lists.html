{layout name="layout1" /}
<div class="wrapper">
    <div class="layui-card">
        <!--操作提示-->
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*资质包管理用于创建和管理资质条目，一个资质包可以包含多个资质证书。</p>
                        <p>*资质包可以被关联到商品分类或单个商品，请谨慎操作。</p>
                    </div>
                </div>
            </div>
        </div>

        <!--搜索条件-->
        <div class="layui-card-body layui-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">资质包名称：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="name" id="package_name" placeholder="请输入资质包名称" autocomplete="off"
                            class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn layui-btn-sm layui-btn-normal" lay-submit lay-filter="package-search">
                        <i class="layui-icon layui-icon-search"></i>查询
                    </button>
                    <button class="layui-btn layui-btn-sm layui-btn-primary" lay-submit
                        lay-filter="package-clear-search">重置</button>
                </div>
            </div>
        </div>

        <div class="layui-card-body">
            <!--添加按钮-->
            <div style="padding-bottom: 10px;">
                <button class="layui-btn layui-btn-sm layuiadmin-btn-package {$view_theme_color}"
                    data-type="add">新增资质包</button>
            </div>

            <!--表格-->
            <table id="package-lists" lay-filter="package-lists"></table>

            <script type="text/html" id="statusTpl">
                <input type="checkbox" lay-filter="switch-status" data-id="{{d.id}}" data-field="status" lay-skin="switch"
                       lay-text="启用|禁用" {{# if(d.status == 1){ }} checked {{# } }} />
            </script>

            <script type="text/html" id="boundCategoriesTpl">
                {{# if(d.bound_categories && d.bound_categories.length > 0){ }}
                    {{# layui.each(d.bound_categories, function(index, item){ }}
                        <span class="layui-badge layui-bg-orange" style="margin: 2px;">{{item}}</span>
                    {{# }); }}
                {{# } else { }}
                    <span style="color: #999;">未绑定分类</span>
                {{# } }}
            </script>

            <script type="text/html" id="selectionModeTpl">
                {{# if(d.selection_mode && d.selection_mode.text){ }}
                    {{ d.selection_mode.text }}
                {{# } else { }}
                    {{# if(d.selection_mode == 1){ }}
                        全部需要
                    {{# } else { }}
                        任选其一
                    {{# } }}
                {{# } }}
            </script>

            <script type="text/html" id="qualificationsTpl">
                {{# if(d.qualifications && d.qualifications.length > 0){ }}
                    {{# layui.each(d.qualifications, function(index, item){ }}
                        <span class="layui-badge layui-bg-blue" style="margin: 2px;">{{item.name}}</span>
                    {{# }); }}
                {{# } else { }}
                    <span style="color: #999;">未包含任何资质</span>
                {{# } }}
            </script>

            <script type="text/html" id="package-operation">
                <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>编辑</a>
                <a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="bind"><i class="layui-icon layui-icon-link"></i>绑定分类</a>
                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><i class="layui-icon layui-icon-delete"></i>删除</a>
            </script>
        </div>
    </div>
</div>

<script>
    layui.use(['table', 'form'], function () {
        var form = layui.form,
            table = layui.table;

        var active = {
            add: function () {
                layer.open({
                    type: 2,
                    title: '新增资质包',
                    content: '{:url("goods.package/add")}',
                    area: ['60%', '70%'],
                    btn: ['确定', '取消'],
                    yes: function (index, layero) {
                        var iframeWindow = window['layui-layer-iframe' + index],
                            submitID = 'package-submit',
                            submit = layero.find('iframe').contents().find('#' + submitID);

                        iframeWindow.layui.form.on('submit(' + submitID + ')', function (data) {
                            var field = data.field;
                            like.ajax({
                                url: '{:url("goods.package/add")}',
                                data: field,
                                type: "post",
                                success: function (res) {
                                    if (res.code == 1) {
                                        layui.layer.msg(res.msg, { icon: 1, time: 1000 });
                                        layer.close(index);
                                        table.reload('package-lists');
                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                    }
                });
            }
        };

        $('.layuiadmin-btn-package').on('click', function () {
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });

        form.on('submit(package-search)', function (data) {
            var field = data.field;
            table.reload('package-lists', {
                where: { name: field.name },
                page: { curr: 1 }
            });
            return false;
        });

        form.on('submit(package-clear-search)', function () {
            $('input[name="name"]').val('');
            table.reload('package-lists', {
                where: {},
                page: { curr: 1 }
            });
            return false;
        });

        like.tableLists('#package-lists', '{:url("goods.package/lists")}', [
        {field: 'id', width: 60, title: 'ID', sort: true}
        ,{field: 'name', title: '资质包名称', align:"center"}
        ,{field: 'qualifications', title: '包含资质', align:"center", templet: '#qualificationsTpl'}
        ,{field: 'selection_mode', title: '选择模式', width: 120, align:"center", templet: '#selectionModeTpl'}
        ,{field: 'bound_categories', title: '绑定分类', align:"center", templet: '#boundCategoriesTpl'}
        ,{field: 'status', title: '状态', align:"center", templet:'#statusTpl'}
        ,{field: 'sort', title: '排序', width: 80, align:"center", sort: true}
        ,{title: '操作', align: 'center', fixed: 'right', toolbar: '#package-operation'}
    ]);

        // 状态切换
        form.on('switch(switch-status)', function (obj) {
            var id = obj.elem.getAttribute('data-id');
            var field = obj.elem.getAttribute('data-field');
            var value = obj.elem.checked ? 1 : 0;

            like.ajax({
                url: '{:url("goods.package/status")}',
                data: {
                    'id': id,
                    'field': field,
                    'value': value
                },
                type: "post",
                success: function (res) {
                    if (res.code == 1) {
                        layui.layer.msg(res.msg, { icon: 1, time: 1000 });
                    } else {
                        // 如果失败，恢复开关状态
                        obj.elem.checked = !obj.elem.checked;
                        form.render('checkbox');
                        layui.layer.msg(res.msg, { icon: 2, time: 2000 });
                    }
                }
            });
        });

        // 表格工具栏事件
        table.on('tool(package-lists)', function (obj) {
            if (obj.event === 'del') {
                var id = obj.data.id;
                layer.confirm('确定删除资质包: <span style="color: red;">' + obj.data.name + '</span>？', function (index) {
                    like.ajax({
                        url: '{:url("goods.package/del")}',
                        data: { 'id': id },
                        type: "post",
                        success: function (res) {
                            if (res.code == 1) {
                                obj.del();
                                layer.close(index);
                                layui.layer.msg(res.msg, { icon: 1, time: 1000 });
                            }
                        }
                    });
                });
            } else if (obj.event === 'edit') {
                var id = obj.data.id;
                layer.open({
                    type: 2,
                    title: '编辑资质包',
                    content: '{:url("goods.package/edit")}?id=' + id,
                    area: ['60%', '70%'],
                    btn: ['确定', '取消'],
                    yes: function (index, layero) {
                        var iframeWindow = window['layui-layer-iframe' + index],
                            submitID = 'package-submit-edit',
                            submit = layero.find('iframe').contents().find('#' + submitID);

                        iframeWindow.layui.form.on('submit(' + submitID + ')', function (data) {
                            var field = data.field;
                            like.ajax({
                                url: '{:url("goods.package/edit")}',
                                data: field,
                                type: "post",
                                success: function (res) {
                                    if (res.code == 1) {
                                        layui.layer.msg(res.msg, { icon: 1, time: 1000 });
                                        layer.close(index);
                                        table.reload('package-lists');
                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                    }
                });
            } else if (obj.event === 'bind') {
                var id = obj.data.id;
                layer.open({
                    type: 2,
                    title: '绑定分类 - ' + obj.data.name,
                    content: '{:url("goods.package/bindCategories")}?id=' + id,
                    area: ['60%', '70%'],
                    btn: ['关闭'],
                    btn1: function (index, layero) {
                        layer.close(index);
                        table.reload('package-lists');
                    }
                });
            }
        });
    });
</script>