<?php

namespace app\shop\controller;

use app\common\basics\ShopBase;
use app\shop\logic\ShopQualificationLogic;
use app\common\server\JsonServer;

/**
 * 商品资质管理
 * Class GoodsQualification
 * @package app\shop\controller
 */
class GoodsQualification extends ShopBase
{
    /**
     * 商品资质列表页面
     */
    public function index()
    {
        $goodsId = $this->request->param('goods_id', 0, 'intval');
        if (!$goodsId) {
            $this->error('商品ID不能为空');
        }

        // 获取商品信息
        $goods = \app\common\model\goods\Goods::find($goodsId);
        if (!$goods || $goods->shop_id != $this->shop_id) {
            $this->error('商品不存在或无权限访问');
        }

        $this->assign([
            'goods_id' => $goodsId,
            'goods' => $goods
        ]);

        return $this->fetch();
    }

    /**
     * 获取商品需要的资质列表
     */
    public function getRequiredQualifications()
    {
        $goodsId = $this->request->param('goods_id', 0, 'intval');
        if (!$goodsId) {
            return JsonServer::error('商品ID不能为空');
        }

        // 验证商品权限
        $goods = \app\common\model\goods\Goods::where([
            'id' => $goodsId,
            'shop_id' => $this->shop_id
        ])->find();

        if (!$goods) {
            return JsonServer::error('商品不存在或无权限访问');
        }

        $qualifications = ShopQualificationLogic::getGoodsRequiredQualifications($this->shop_id, $goodsId);
        return JsonServer::success('获取成功', $qualifications);
    }

    /**
     * 上传商品资质
     */
    public function upload()
    {
        if ($this->request->isPost()) {
            $post = $this->request->post();
            $post['goods_id'] = intval($post['goods_id'] ?? 0);

            // 验证商品权限
            if ($post['goods_id'] > 0) {
                $goods = \app\common\model\goods\Goods::where([
                    'id' => $post['goods_id'],
                    'shop_id' => $this->shop_id
                ])->find();

                if (!$goods) {
                    return JsonServer::error('商品不存在或无权限访问');
                }
            }

            $result = ShopQualificationLogic::upload($this->shop_id, $post);
            if ($result) {
                return JsonServer::success('上传成功');
            } else {
                return JsonServer::error(ShopQualificationLogic::getError() ?: '上传失败');
            }
        }

        $goodsId = $this->request->param('goods_id', 0, 'intval');
        $qualificationId = $this->request->param('qualification_id', 0, 'intval');
        $qualificationName = $this->request->param('qualification_name', '');

        // 获取资质详细信息
        $qualification = null;
        if ($qualificationId) {
            $qualification = \app\common\model\goods\Qualification::where([
                'id' => $qualificationId, 
                'status' => 1, 
                'del' => 0
            ])
            ->field('id,name,description,document_path,document_name,ex_img')
            ->find();
        }

        // 获取商品信息
        $goods = null;
        if ($goodsId) {
            $goods = \app\common\model\goods\Goods::where([
                'id' => $goodsId,
                'shop_id' => $this->shop_id
            ])->field('id,name,shop_id')->find();
        }

        $this->assign([
            'goods_id' => $goodsId,
            'goods' => $goods,
            'qualification_id' => $qualificationId,
            'qualification_name' => $qualificationName,
            'qualification' => $qualification
        ]);

        return $this->fetch();
    }

    /**
     * 检查商品资质完整性
     */
    public function checkComplete()
    {
        $goodsId = $this->request->param('goods_id', 0, 'intval');
        if (!$goodsId) {
            return JsonServer::error('商品ID不能为空');
        }

        // 验证商品权限
        $goods = \app\common\model\goods\Goods::where([
            'id' => $goodsId,
            'shop_id' => $this->shop_id
        ])->find();

        if (!$goods) {
            return JsonServer::error('商品不存在或无权限访问');
        }

        $isComplete = ShopQualificationLogic::checkGoodsQualificationComplete($this->shop_id, $goodsId);
        $requiredQualifications = ShopQualificationLogic::getGoodsRequiredQualifications($this->shop_id, $goodsId);

        return JsonServer::success('检查完成', [
            'is_complete' => $isComplete,
            'required_qualifications' => $requiredQualifications
        ]);
    }

    /**
     * 删除商品资质
     */
    public function delete()
    {
        $id = $this->request->param('id', 0, 'intval');
        if (!$id) {
            return JsonServer::error('参数错误');
        }

        // 验证资质权限
        $qualification = \app\common\model\shop\ShopQualification::where([
            'id' => $id,
            'shop_id' => $this->shop_id,
            'del' => 0
        ])->find();

        if (!$qualification) {
            return JsonServer::error('资质不存在或无权限操作');
        }

        $result = ShopQualificationLogic::delete($this->shop_id, $id);
        if ($result) {
            return JsonServer::success('删除成功');
        } else {
            return JsonServer::error(ShopQualificationLogic::getError() ?: '删除失败');
        }
    }

    /**
     * 获取商品列表（用于选择商品）
     */
    public function getGoodsList()
    {
        $keyword = $this->request->param('keyword', '');
        $page = $this->request->param('page', 1, 'intval');
        $limit = $this->request->param('limit', 20, 'intval');

        $where = [
            'shop_id' => $this->shop_id,
            'del' => 0
        ];

        if (!empty($keyword)) {
            $where[] = ['name', 'like', '%' . trim($keyword) . '%'];
        }

        $result = \app\common\model\goods\Goods::where($where)
            ->field('id,name,image,status')
            ->order('create_time', 'desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);

        return JsonServer::success('获取成功', [
            'list' => $result->items(),
            'count' => $result->total(),
            'page' => $page,
            'limit' => $limit
        ]);
    }
}
