<?php
// +----------------------------------------------------------------------
// | kshop
// +----------------------------------------------------------------------
// | Copyright (c) 2022~2024 kshop All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( https://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: kshop
// +----------------------------------------------------------------------
namespace app\model\goods;

use app\common\basics\Models;
use app\common\model\goods\Qualification;
use think\model\concern\SoftDelete;

/**
 * 资质包模型
 * Class QualificationPackage
 * @package app\model\goods
 */
class QualificationPackage extends Models
{
    use SoftDelete;
    protected $deleteTime = 'del';
    protected $defaultSoftDelete = 0;

    protected $name = 'qualification_package';

    /**
     * 关联资质包项目
     */
    public function items()
    {
        return $this->hasMany(QualificationPackageItem::class, 'package_id', 'id');
    }

    /**
     * 关联资质
     * CREATE TABLE `ls_qualification_package_item` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `package_id` int(11) unsigned NOT NULL COMMENT '资质包ID',
  `qualification_id` int(11) unsigned NOT NULL COMMENT '资质ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_package_qualification` (`package_id`,`qualification_id`) USING BTREE,
  KEY `idx_package_id` (`package_id`) USING BTREE,
  KEY `idx_qualification_id` (`qualification_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=73 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='资质包-证书关联表';

CREATE TABLE `ls_qualification` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(100) NOT NULL COMMENT '证件名称',
  `description` text COMMENT '文字描述',
  `ex_img` varchar(255) DEFAULT NULL COMMENT '实例',
  `document_path` varchar(255) DEFAULT '' COMMENT '文档路径',
  `document_name` varchar(255) DEFAULT '' COMMENT '文档原始名称',
  `valid_days` int(11) unsigned DEFAULT '0' COMMENT '有效期天数，0表示永久有效',
  `status` tinyint(1) unsigned DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `is_required` tinyint(1) unsigned DEFAULT '0' COMMENT '是否必传：0-非必传，1-必传',
  `sort` int(11) unsigned DEFAULT '255' COMMENT '排序',
  `del` tinyint(1) unsigned DEFAULT '0' COMMENT '是否删除:1-是;0-否',
  `create_time` int(11) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_status_del` (`status`,`del`) USING BTREE,
  KEY `idx_name` (`name`) USING BTREE,
  KEY `idx_document_path` (`document_path`) USING BTREE,
  KEY `idx_is_required` (`is_required`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='资质管理表';
     */
    public function qualifications()
    {
        return $this->belongsToMany(Qualification::class, 'qualification_package_item', 'qualification_id', 'package_id');
    }

    public function getSelectionModeAttr($value)
    {
        $status = [1 => '全部需要', 2 => '任选其一'];
        return ['text' => $status[$value], 'value' => $value];
    }
}
