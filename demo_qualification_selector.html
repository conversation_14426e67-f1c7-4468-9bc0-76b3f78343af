<!DOCTYPE html>
<html>
<head>
    <title>资质选择器演示</title>
    <link rel="stylesheet" href="https://unpkg.com/layui@2.8.18/dist/css/layui.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .demo-container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .qualification-item:hover { transform: translateY(-1px); }
        .selected-tag:hover { background: #40a9ff !important; }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 style="text-align: center; color: #333; margin-bottom: 30px;">资质选择器演示</h1>
        
        <div class="layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label" style="color: #6a6f6c; width: 140px;">
                    <span style="color: red;">*</span>选择资质：
                </label>
                <div class="layui-input-block" style="margin-left: 170px;">
                    <!-- 搜索框 -->
                    <div style="margin-bottom: 10px;">
                        <input type="text" id="qualification-search" placeholder="搜索资质名称..." class="layui-input" style="width: 300px; display: inline-block;">
                        <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" onclick="selectAll()">全选</button>
                        <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" onclick="selectNone()">全不选</button>
                        <span style="margin-left: 10px; color: #999;">已选择: <span id="selected-count">0</span> 个</span>
                    </div>
                    
                    <!-- 资质选择区域 -->
                    <div id="qualification-container" style="border: 1px solid #e6e6e6; border-radius: 4px; background: #fff;">
                        <!-- 已选择的资质 -->
                        <div id="selected-qualifications" style="min-height: 60px; padding: 10px; border-bottom: 1px solid #e6e6e6; background: #f8f9fa;">
                            <div style="color: #666; font-size: 12px; margin-bottom: 5px;">已选择的资质：</div>
                            <div id="selected-tags"></div>
                        </div>
                        
                        <!-- 可选择的资质列表 -->
                        <div style="padding: 10px;">
                            <div style="color: #666; font-size: 12px; margin-bottom: 10px;">可选择的资质：</div>
                            <div id="qualification-list" style="max-height: 250px; overflow-y: auto;">
                                <!-- 模拟资质数据 -->
                                <div class="qualification-item" data-id="1" data-name="食品经营许可证" data-description="食品经营相关资质证明" data-valid-days="365" style="margin: 5px 0; padding: 8px; border: 1px solid #e6e6e6; border-radius: 4px; cursor: pointer; transition: all 0.3s;">
                                    <div style="display: flex; align-items: center;">
                                        <input type="checkbox" name="qualification_ids[]" value="1" style="margin-right: 8px;">
                                        <div style="flex: 1;">
                                            <div style="font-weight: bold; color: #333;">食品经营许可证</div>
                                            <div style="font-size: 12px; color: #666; margin-top: 2px;">食品经营相关资质证明 • 365天有效</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="qualification-item" data-id="2" data-name="医疗器械经营许可证" data-description="医疗器械经营资质" data-valid-days="0" style="margin: 5px 0; padding: 8px; border: 1px solid #e6e6e6; border-radius: 4px; cursor: pointer; transition: all 0.3s;">
                                    <div style="display: flex; align-items: center;">
                                        <input type="checkbox" name="qualification_ids[]" value="2" style="margin-right: 8px;">
                                        <div style="flex: 1;">
                                            <div style="font-weight: bold; color: #333;">医疗器械经营许可证</div>
                                            <div style="font-size: 12px; color: #666; margin-top: 2px;">医疗器械经营资质 • 永久有效</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="qualification-item" data-id="3" data-name="化妆品生产许可证" data-description="化妆品生产相关证明" data-valid-days="730" style="margin: 5px 0; padding: 8px; border: 1px solid #e6e6e6; border-radius: 4px; cursor: pointer; transition: all 0.3s;">
                                    <div style="display: flex; align-items: center;">
                                        <input type="checkbox" name="qualification_ids[]" value="3" style="margin-right: 8px;">
                                        <div style="flex: 1;">
                                            <div style="font-weight: bold; color: #333;">化妆品生产许可证</div>
                                            <div style="font-size: 12px; color: #666; margin-top: 2px;">化妆品生产相关证明 • 730天有效</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="qualification-item" data-id="4" data-name="CCC安全认证证书" data-description="产品安全认证" data-valid-days="1095" style="margin: 5px 0; padding: 8px; border: 1px solid #e6e6e6; border-radius: 4px; cursor: pointer; transition: all 0.3s;">
                                    <div style="display: flex; align-items: center;">
                                        <input type="checkbox" name="qualification_ids[]" value="4" style="margin-right: 8px;">
                                        <div style="flex: 1;">
                                            <div style="font-weight: bold; color: #333;">CCC安全认证证书</div>
                                            <div style="font-size: 12px; color: #666; margin-top: 2px;">产品安全认证 • 1095天有效</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="qualification-item" data-id="5" data-name="营业执照" data-description="企业营业执照" data-valid-days="0" style="margin: 5px 0; padding: 8px; border: 1px solid #e6e6e6; border-radius: 4px; cursor: pointer; transition: all 0.3s;">
                                    <div style="display: flex; align-items: center;">
                                        <input type="checkbox" name="qualification_ids[]" value="5" style="margin-right: 8px;">
                                        <div style="flex: 1;">
                                            <div style="font-weight: bold; color: #333;">营业执照</div>
                                            <div style="font-size: 12px; color: #666; margin-top: 2px;">企业营业执照 • 永久有效</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-mid layui-word-aux">请至少选择一个资质证书</div>
                </div>
            </div>
            
            <div class="layui-form-item" style="margin-top: 30px;">
                <div class="layui-input-block" style="margin-left: 170px;">
                    <button type="button" class="layui-btn" onclick="getSelectedValues()">获取选中值</button>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="resetSelection()">重置</button>
                </div>
            </div>
        </div>
        
        <div id="result" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px; display: none;">
            <h3>选中的资质：</h3>
            <pre id="result-content"></pre>
        </div>
    </div>

    <script>
        // 初始化资质选择器
        $(document).ready(function() {
            initQualificationSelector();
        });

        function initQualificationSelector() {
            updateSelectedDisplay();
            
            // 搜索功能
            $('#qualification-search').on('input', function() {
                var keyword = $(this).val().toLowerCase();
                $('.qualification-item').each(function() {
                    var name = $(this).data('name').toLowerCase();
                    var description = $(this).data('description').toLowerCase();
                    if (name.indexOf(keyword) !== -1 || description.indexOf(keyword) !== -1) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            });

            // 点击资质项切换选择状态
            $(document).on('click', '.qualification-item', function(e) {
                if (e.target.type !== 'checkbox') {
                    var checkbox = $(this).find('input[type="checkbox"]');
                    checkbox.prop('checked', !checkbox.prop('checked'));
                }
                updateItemStyle($(this));
                updateSelectedDisplay();
            });

            // 监听复选框变化
            $(document).on('change', 'input[name="qualification_ids[]"]', function() {
                updateItemStyle($(this).closest('.qualification-item'));
                updateSelectedDisplay();
            });
        }

        // 更新项目样式
        function updateItemStyle(item) {
            var checkbox = item.find('input[type="checkbox"]');
            if (checkbox.prop('checked')) {
                item.css({
                    'background-color': '#f0f9ff',
                    'border-color': '#1890ff',
                    'box-shadow': '0 2px 4px rgba(24,144,255,0.2)'
                });
                item.attr('data-selected', 'true');
            } else {
                item.css({
                    'background-color': '#fff',
                    'border-color': '#e6e6e6',
                    'box-shadow': 'none'
                });
                item.removeAttr('data-selected');
            }
        }

        // 更新已选择显示
        function updateSelectedDisplay() {
            var selectedItems = $('input[name="qualification_ids[]"]:checked');
            var count = selectedItems.length;
            $('#selected-count').text(count);
            
            var tagsHtml = '';
            selectedItems.each(function() {
                var item = $(this).closest('.qualification-item');
                var name = item.data('name');
                var validDays = item.data('valid-days');
                var validText = validDays == 0 ? '永久有效' : validDays + '天有效';
                
                tagsHtml += '<span class="selected-tag" data-id="' + $(this).val() + '" style="display: inline-block; margin: 2px; padding: 4px 8px; background: #1890ff; color: white; border-radius: 12px; font-size: 12px; cursor: pointer;">' +
                            name + ' (' + validText + ')' +
                            '<i class="layui-icon layui-icon-close" style="margin-left: 5px; font-size: 10px;" onclick="removeSelected(' + $(this).val() + ')"></i>' +
                            '</span>';
            });
            
            if (tagsHtml === '') {
                tagsHtml = '<span style="color: #999; font-style: italic;">暂未选择任何资质</span>';
            }
            
            $('#selected-tags').html(tagsHtml);
            
            // 更新所有项目样式
            $('.qualification-item').each(function() {
                updateItemStyle($(this));
            });
        }

        // 全选
        function selectAll() {
            $('.qualification-item:visible input[type="checkbox"]').prop('checked', true);
            updateSelectedDisplay();
        }

        // 全不选
        function selectNone() {
            $('input[name="qualification_ids[]"]').prop('checked', false);
            updateSelectedDisplay();
        }

        // 移除已选择的资质
        function removeSelected(id) {
            $('input[name="qualification_ids[]"][value="' + id + '"]').prop('checked', false);
            updateSelectedDisplay();
        }

        // 获取选中值
        function getSelectedValues() {
            var selectedValues = [];
            $('input[name="qualification_ids[]"]:checked').each(function() {
                var item = $(this).closest('.qualification-item');
                selectedValues.push({
                    id: $(this).val(),
                    name: item.data('name'),
                    description: item.data('description'),
                    valid_days: item.data('valid-days')
                });
            });
            
            $('#result-content').text(JSON.stringify(selectedValues, null, 2));
            $('#result').show();
        }

        // 重置选择
        function resetSelection() {
            selectNone();
            $('#qualification-search').val('');
            $('.qualification-item').show();
            $('#result').hide();
        }
    </script>
</body>
</html>