{layout name="layout2" /}

<link rel="stylesheet" href="/static/admin/css/goods.css" media="all">
<link href="__PUBLIC__/static/lib/layui/layeditor/layedit.css" rel="stylesheet"/>
<script src="__PUBLIC__/static/lib/layui/layeditor/index.js"></script>
<script src="__PUBLIC__/static/lib/layui/layeditor/ace/ace.js"></script>

<div class="layui-tab layui-tab-card">
    <!--顶部切换页-->
    <ul class="layui-tab-title">
        <li class="goods-tab layui-this" style="color: #6a6f6c">基础设置</li>
        <li class="goods-tab" style="color: #6a6f6c">规格型号</li>
        <li class="goods-tab" style="color: #6a6f6c">商品详情</li>
        <li class="goods-tab" style="color: #6a6f6c">销售设置</li>
<!--        <li class="goods-tab" style="color: #6a6f6c">分销设置</li>-->
    </ul>

    <!--切换内容-->
    <div class="layui-tab-content layui-form">
        <!--基础信息-->
        {include file="goods/goods/goods_base" /}
        <!--规格型号-->
        {include file="goods/goods/goods_spec" /}
        <!--商品详情-->
        {include file="goods/goods/goods_content" /}
        <!--销售设置-->
        {include file="goods/goods/goods_sale_setting" /}
        <div class="layui-form-item layui-hide">
            <input type="button" lay-submit lay-filter="goods-submit" id="goods-submit" value="确认">
        </div>
    </div>
</div>

<script src="__PUBLIC__/static/common/js/array.js"></script>

<script>
    var dragstart = 0;
    var swop_element_ed = -1;
    var create_table_by_spec = null;
    var spec_table_data = [];
    var spec_value_temp_id_number = 0;

    layui.config({
        version:"{$front_version}",
        base: '/static/lib/'
    }).extend({
        likeedit: 'likeedit/likeedit'
    }).use(['table', 'form', 'element', 'layEditor'], function() {
        var form = layui.form
            ,$ = layui.$
            , element = layui.element
            // , likeedit = layui.likeedit;
            , layEditor = layui.layEditor;

        //---------------------------------------平台分类联动 begin ----------------------------------
        var categorys = {$category_lists | raw}; 
        setSelectFirst();

        function setSelectFirst(default_id) {
            // 使用优化后的setSelect函数，传入parentId为0来显示第一级分类
            like.setSelect(default_id, categorys, "first_cate_id", '分类', 0);
        }

        function setSelectSecond(default_id, pid) {
            pid = pid === undefined ? $('select[name="first_cate_id"]').val() : pid;
            // 清空第三级分类
            $('select[name="third_cate_id"]').html('<option value="">请选择分类</option>');
            form.render('select');
            // 使用优化后的setSelect函数，传入parentId参数来正确处理级联选择
            like.setSelect(default_id, categorys, "second_cate_id", '分类', pid);
        }

        // 监听单选按钮的变化
        form.on('radio(groupBuyFilter)', function(data) {
            var isGroupBuy = data.value === '1';
            // var groupBuyPriceInput = $('.group-buy-price-input');
            //
            // if (isGroupBuy) {
            //     groupBuyPriceInput.show();
            // } else {
            //     groupBuyPriceInput.hide();
            //     // 可选：清空拼单集采价输入框
            //     groupBuyPriceInput.val('');
            // }

            // 重新渲染表单，以确保显示/隐藏效果生效
            // form.render();
        });


        function setSelectThird(default_id, pid) {
            pid = pid === undefined ? $('select[name="second_cate_id"]').val() : pid;
            // 使用优化后的setSelect函数，传入parentId参数来正确处理级联选择
            like.setSelect(default_id, categorys, "third_cate_id", '分类', pid);
        }
        // 监听一级分类选择
        form.on('select(first_category)', function (data) {
            setSelectSecond('', data.value);
            checkCategoryQualifications();
        });
        // 监听二级分类选择
        form.on('select(second_category)', function (data) {
            setSelectThird('', data.value);
            checkCategoryQualifications();
        });
        // 监听三级分类选择
        form.on('select(third_category)', function (data) {
            checkCategoryQualifications();
        });
        //---------------------------------------平台分类联动 end ----------------------------------

        // -------------------------------------- 下拉菜单渲染 begin -------------------------------
        var shop_category_lists = {$shop_category_lists | raw}; // 店铺商品分类
        var unit = {$unit_lists | raw};
        var brands = {$brand_lists | raw};
        var supplier = {$supplier_lists | raw};
        var freight = {$freight_lists | raw};

        //渲染商家分类（显示第一级分类，pid=0）
        like.setSelect('', shop_category_lists, "shop_cate_id", '分类', 0);
        //渲染单位
        like.setSelect('', unit, "unit_id", '单位');
        //渲染品牌
        like.setSelect('', brands, "brand_id", '品牌');
        //渲染供应商
        like.setSelect('', supplier, "supplier_id", '供应商');
        //渲染运费模板
        like.setSelect('', freight, "express_template_id", '运费模板');
        // -------------------------------------- 下拉菜单渲染 end ---------------------------------

        // ----------------------------------------图片/视频上传 begin -----------------------------
        // 监听图片删除
        like.delUpload();
        // 商品封面图
        $(document).on("click", "#image", function () {
            like.imageUpload({
                limit: 1,
                field: "image",
                that: $(this),
                content: '{:url("file/lists")}?type=10'
            });
        })
        // 商品分享海报
        $(document).on("click", "#poster", function () {
            like.imageUpload({
                limit: 1,
                field: "poster",
                that: $(this),
                content: '{:url("file/lists")}?type=10'
            });
        })
        // 商品轮播图
        $(document).on("click", "#goodsimage", function () {
            like.imageUpload({
                limit: 5,
                field: "goods_image[]",
                that: $(this),
                content: '/shop/file/lists?type=10'
            });
        })
        // 商品视频
        $(document).on("click", "#video", function () {
            like.videoUpload({
                limit: 1,
                field: "video",
                that: $(this),
                content: '/shop/file/videoList'
            });
        })
        // 统一规格-规格图片
        $(document).on("click", "#one_spec_image", function () {
            like.imageUpload({
                limit: 1,
                field: "one_spec_image",
                that: $(this),
                content: '/shop/file/lists?type=10'
            });
        })
        // 多规格-规格图片
        $(document).on("click", ".more_spec_image", function () {
           like.imageUpload({
                limit: 1,
                field: "spec_image[]",
                that: $(this),
                content: '/shop/file/lists?type=10',
                isSpecImage: true
           });
        })
        // 监听编辑时多规格图片删除按钮
        $(document).on('mouseenter', '.goods-spec-img-div', function () {
            $(this).find('.goods-spec-img-del-x').show();
        });
        $(document).on('mouseleave', '.goods-spec-img-div', function () {
            $(this).find('.goods-spec-img-del-x').hide();
        });
        $(document).on('click', '.goods-spec-img-del-x', function () {
            var key = 'spec_image[]' + $(this).parent().parent().parent().attr('spec-value-temp-ids');
            $(this).parent().html('<div class="like-upload-image goods-spec-img-div">' +
                '<input class="upload-spec-image"  type="hidden" name="spec_image[]">' +
                '<div class="upload-image-elem">' +
                '<a class="add-upload-image more_spec_image"> + 添加图片</a>' +
                '</div>' +
                '</div>');
            spec_table_data[key] = '';

        });
        // ----------------------------------------图片/视频上传 end -----------------------------

        //------------------------------------------数据验证 begin -------------------------------
        function switchTab(number) {
            $('.goods-tab').removeClass('layui-this');
            $('.goods-content').removeClass('layui-show');
            $('.goods-tab').eq(number).addClass('layui-this');
            $('.goods-content').eq(number).addClass('layui-show');
        }

        form.verify({
            custom_required: function (value, item) {
                if (!$.trim(value)) {
                    switchTab($(item).attr('switch-tab'));
                    return $(item).attr('verify-msg');
                }
            },
            status:function(value,item){
                if(!$('input[name="status"]:checked').val()){
                    return $(item).attr('verify-msg');
                }
            },
            one_spec_required: function (value, item) {
                if ($('input[name="spec_type"]:checked').val() == 1) {
                    if (!value) {
                        switchTab($(item).attr('switch-tab'));
                        return $(item).attr('verify-msg');
                    }
                }
            },
            add_more_spec: function (value, item) {
                if ($('input[name="spec_type"]:checked').val() == 2) {
                    if ($('#more-spec-lists-table tbody tr').length == 0) {
                        switchTab($(item).attr('switch-tab'));
                        return $(item).attr('verify-msg');
                    }
                }
            },
            more_spec_required: function (value, item) {
                if ($('input[name="spec_type"]:checked').val() == 2) {
                    if (!value) {
                        switchTab($(item).attr('switch-tab'));
                        return $(item).attr('verify-msg');
                    }
                }
            },
            one_volume: function (value, item) {
                if ($('input[name="spec_type"]:checked').val() == 1) {
                    if (value && value < 0) {
                        switchTab($(item).attr('switch-tab'));
                        return '体积必须大于0';
                    }
                }
            },
            one_weight: function (value, item) {
                if ($('input[name="spec_type"]:checked').val() == 1) {
                    if (value && value < 0) {
                        switchTab($(item).attr('switch-tab'));
                        return '重量必须大于0';
                    }
                }
            },
            one_market_price: function (value, item) {
                if ($('input[name="spec_type"]:checked').val() == 1) {
                    if (value && value <= 0) {
                        switchTab($(item).attr('switch-tab'));
                        return '市场价必须大于0';
                    }
                }
            },
            one_price: function (value, item) {
                if ($('input[name="spec_type"]:checked').val() == 2) {
                    if (value && value <= 0) {
                        switchTab($(item).attr('switch-tab'));
                        return '价格必须大于0';
                    }
                }
            },
            one_chengben_price: function (value, item) {
                if ($('input[name="spec_type"]:checked').val() == 2) {
                    if (value && value <= 0) {
                        switchTab($(item).attr('switch-tab'));
                        return '成本价必须大于0';
                    }
                }
            },
            more_market_price:function (value, item) {
                if ($('input[name="spec_type"]:checked').val() == 2) {
                    if (value && value <= 0) {
                        switchTab($(item).attr('switch-tab'));
                        return '市场价必须大于0';
                    }
                }
            },
            more_price:function (value, item) {
                if ($('input[name="spec_type"]:checked').val() == 2) {
                    if (value && value < 0.01) {
                        switchTab($(item).attr('switch-tab'));
                        return '价格必须大于或等于0.01';
                    }
                }
            },
            more_chengben_price:function (value, item) {
                if ($('input[name="spec_type"]:checked').val() == 2) {
                    if (value && value <= 0) {
                        switchTab($(item).attr('switch-tab'));
                        return '成本价格必须大于0';
                    }
                }
            },
            more_stock: function (value, item) {
                if ($('input[name="spec_type"]:checked').val() == 2) {
                    if (value && value < 0) {
                        switchTab($(item).attr('switch-tab'));
                        return '库存必须大于0';
                    }
                }
            },
            more_weight: function (value, item) {
                if ($('input[name="spec_type"]:checked').val() == 2) {
                    if (value && value < 0) {
                        switchTab($(item).attr('switch-tab'));
                        return '重量必须大于0';
                    }
                }
            },
            more_volume: function (value, item) {
                if ($('input[name="spec_type"]:checked').val() == 2) {
                    if (value && value < 0) {
                        switchTab($(item).attr('switch-tab'));
                        return '体积必须大于0';
                    }
                }
            },
            repetition_spec_name: function (value, item) {
                if ($('input[name="spec_type"]:checked').val() == 2) {
                    var spec_names = [];
                    $('.spec_name').each(function () {
                        spec_names.push($(this).val());
                    });
                    if ((new Set(spec_names)).size != spec_names.length) {
                        switchTab($(item).attr('switch-tab'));
                        return '规格名称重复';
                    }
                }
            },
            repetition_spec_value: function (value, item) {
                if ($('input[name="spec_type"]:checked').val() == 2) {
                    var spec_values = [];
                    $(item).find('.goods-spec-value-input').each(function () {
                        spec_values.push($(this).val());
                    });
                    if ((new Set(spec_values)).size != spec_values.length) {
                        switchTab($(item).attr('switch-tab'));
                        return '同一规格中，规格值不能重复';
                    }
                }
            },
            distribution:function (value,item) {
                var first_ratio = parseFloat($('.first_ratio').val());
                var second_ratio = parseFloat($('.second_ratio').val());
                var three_ratio = parseFloat(value);
                if(first_ratio + second_ratio + three_ratio > 100){
                    return '分销比例不可超过100';
                }

            }
        });
        //------------------------------------------数据验证 end ----------------------------------
        //------------------------------------------规格型号 begin --------------------------------
        //监听多规格/单规格按钮
        form.on('radio(spec-type)', function (data) {
            switchSpecType(data.value);
        });

        // 统一规格与多规格切换事件
        function switchSpecType(value) {
            var goods_spec_project = $('#goods-spec-project'); // 规格项区域

            if (value == 2) { // 多规格
                $('#add-spec').parent().show(); // 显示添加规格项目按钮

                if (goods_spec_project.children().length > 0) { // 判断规格项区域是否有子元素
                    goods_spec_project.parent().show(); // 显示规格项区域
                    $('#more-spec-lists').show(); // 显示多规格明细
                }
                $('#one-spec-lists').hide(); // 隐藏统一规格规格明细
            } else {
                $('#add-spec').parent().hide(); // 隐藏 添加规格项目 按钮
                goods_spec_project.parent().hide(); // 隐藏规格项区域
                $('#more-spec-lists').hide(); // 隐藏多规格明细
                $('#one-spec-lists').show(); // 显示单规格明细
            }
        }

        //监听添加规格项按钮
        $(document).on('click', '#add-spec', function () {
            addSpec();
        });

        //添加规格项
        function addSpec(value) {
            value = value === undefined ? ' ' : value;
            var element_spec = $('#goods-spec-project'); // 规格项区域
            var count = $('.goods-spec').length; // 规格项数量
            if (count > 2) {
                layer.msg('最多添加3个规格项目');
                return;
            }
            var template_spec = $('#template-spec').html(); // 获取规格项目模板
            // 使用value值替换规格项目模板中{value}占位符，并追加至规格项区域中
            element_spec.append(template_spec.replace('{value}', value));
            $('#goods-spec-project').parent().show();
            form.render('checkbox');
        }

        // 鼠标移入显示删除规格项按钮
        $(document).on('mouseenter', '.goods-spec', function () {
            $(this).find('.goods-spec-del-x').show();
        });

        // 鼠标移出隐藏删除规格项按钮
        $(document).on('mouseleave', '.goods-spec', function () {
            $(this).find('.goods-spec-del-x').hide();
        });

        // 监听删除规格项目按钮
        $(document).on('click', '.goods-spec-del-x', function () {
            $(this).parent().remove(); // 移除当前规格项目
            var goods_spec_project = $('#goods-spec-project');
            if (goods_spec_project.children().length == 0) { // 规格项区域中若没有子元素则隐藏
                goods_spec_project.parent().hide();
            }
            // 触发生成表格函数
            triggerCreateTableBySepc();
        });

        // 监听规格项输入
        $(document).on('input', '.goods-spec input', function () {
            triggerCreateTableBySepc();
            specValueLater();
        });

        // 触发生成规格明细表格
        function triggerCreateTableBySepc() {
            clearTimeout(create_table_by_spec);
            create_table_by_spec = setTimeout(createTableBySepc, 1000);
        }

        // 生成规格明细表格
        function createTableBySepc() {
            if ($('.goods-spec').length <= 0) { // 没有规格项目，隐藏多规格明细
                $('#more-spec-lists').hide();
                return;
            }
            $('#more-spec-lists').show(); // 显示多规格明细
            var table_title = []; // 用于保存 规格项的值
            var table_data = [];  // 规格项数据
            var spec_value_temp_arr = []; // 规格值临时数组
            var i = 0;
            var table_html = '';
            var th_html = $('#template-spec-table-th').html(); // 多规格表头模板
            var tr_html = $('#template-spec-table-tr').html(); // 多规格行模板

            //遍历规格项目
            $('.goods-spec').each(function () {
                var spec_name = $(this).find('.spec_name').first().val(); // 规格项的值 例：颜色
                if (isEmptyString(spec_name)) {
                    return true;
                }
                table_title[i] = spec_name; // 保存 规格项的值  例：['颜色']
                table_data[i] = []; // 例: [[]]
                spec_value_temp_arr[i] = []; // 例：[[]]
                var j = 0;
                // 遍历 当前规格项目 下的所有 规格值
                $(this).find('.goods-spec-value .goods-spec-value-input').each(function () {
                    var spec_value = $(this).val(); // 规格值 例：
                    var spec_value_temp_id = $(this).attr('spec-value-temp-id'); // 规格值临时id
                    if (isEmptyString(spec_value)) {
                        return true;
                    }
                    table_data[i][j] = spec_value; // 将 规格值 保存至 规格项 中
                    spec_value_temp_arr[i][j] = spec_value_temp_id; // 将 规格值临时id 保存至 规格值临时数组 中
                    j++;
                });
                i++;
            });

            //表格头部组装
            spec_th_html = '';
            for (var i in table_title) {
                spec_th_html += '<th>' + table_title[i] + '</th>';
            }
            table_html = th_html.replace('{spec_th}', spec_th_html);
            // 笛卡尔积, 组装SKU 例：[['颜色', 'S码'], ['颜色', 'M码']]
            spec_value_temp_arr = cartesianProduct(spec_value_temp_arr);
            table_data = cartesianProduct(table_data);
            for (var i in table_data) {
                var spec_tr_html = '';
                var tr_name_arr = [];
                var specs = '';
                if (Array.isArray(table_data[i])) {
                    //根据规格创建tr
                    var spec_value_temp_ids = '';
                    for (var j in spec_value_temp_arr[i]) {
                        spec_value_temp_ids += spec_value_temp_arr[i][j] + ',';
                    }
                    spec_value_temp_ids = spec_value_temp_ids.substring(0, spec_value_temp_ids.lastIndexOf(','));
                    spec_tr_html += '<tr spec-value-temp-ids="' + spec_value_temp_ids + '">';

                    for (var j in table_data[i]) {
                        spec_tr_html += '<td>' + table_data[i][j] + '</td>';
                        tr_name_arr[j] = table_data[i][j];
                        specs += table_data[i][j].replace(',', '') + ',';
                    }
                } else {
                    var spec_value_temp_ids = spec_value_temp_arr[i];
                    spec_tr_html = '<tr spec-value-temp-ids="' + spec_value_temp_ids + '">';
                    spec_tr_html += '<td>' + table_data[i] + '</td>';
                    specs += table_data[i].replace(',', '') + ',';
                }
                specs = specs.substring(0, specs.lastIndexOf(','));
                spec_table_data["spec_value_str[]" + spec_value_temp_ids] = specs;
                spec_tr_html += '<td style="display: none"><input type="hidden" name="spec_value_str[]" value="' + specs + '"><input type="hidden" name="item_id[]" value=""></td>';
                table_html += tr_html.replace('{spec_td}', spec_tr_html);
            }

            $('#more-spec-lists-table').html(table_html);
            setTableValue();
        };

        //动态渲染已保存的值
        function setTableValue() {
            $('#more-spec-lists-table').find('input').each(function () {
                var key = $(this).attr('name') + $(this).parent().parent().attr('spec-value-temp-ids');
                if (spec_table_data[key] !== undefined) {
                    $(this).val(spec_table_data[key]);
                }
            });
            $('.goods-spec-img-div').each(function () {
                var key = $(this).parent().parent().attr('spec-value-temp-ids');
                if (spec_table_data["spec_image[]" + key]) {
                    $(this).html('<input name="spec_image[]" type="hidden" value="' + spec_table_data["spec_image[]" + key] + '"><a class="goods-spec-img-del-x">x</a><img class="goods-spec-img" src="' + spec_table_data["spec_image[]" + key] + '">');
                }
            });
        }

        // 监听添加规格值链接被点击: 弹出多行输出框，处理输入的规格值数据，遍历每个规格值并生成相应的html
        $(document).on('click', '.add-spec-value', function () {
            var add_spec_value = $(this);
            layer.prompt({title: '输入规格值，多个请换行', formType: 2}, function (text, index) {
                layer.close(index);
                var specs = text.split('\n');
                for (var i in specs) {
                    specs[i] = specs[i].trim();
                }
                specs = unique(specs);
                var added_specs = [];
                add_spec_value.parent().parent().find('.goods-spec-value-input').each(function () {
                    added_specs.push($(this).val().trim());
                });
                for (var i in specs) {
                    var spec = specs[i].trim();
                    if (spec == '' || in_array(spec, added_specs)) {
                        //已存或为空的不添加
                        continue;
                    }
                    addSpecvalue(add_spec_value, spec, 0);
                }
                specValueLater();
            });
        });

        // 添加规格值: 将【数据】填充至【规格值模板】，并将【规格值模板】追加至【添加规格值】链接前
        function addSpecvalue(add_spec_value, spec, spec_id) {
            var template_spec_value = $('#template-spec-value').html();
            var template_spec_value_html = template_spec_value.replace('{spec_value_temp_id}', spec_value_temp_id_number--);
            template_spec_value_html = template_spec_value_html.replace('{spec_value_id}', spec_id);
            template_spec_value_html = template_spec_value_html.replace('{spec_value}', spec)
            add_spec_value.parent().before(template_spec_value_html);
        }

        //处理每项规格值
        function specValueLater() {
            $('.add-spec-value').each(function () {
                add_spec_value = $(this);
                var spec_values = '';
                add_spec_value.parent().parent().find('.goods-spec-value-input').each(function () {
                    spec_values += $(this).val() + ',';
                });
                add_spec_value.parent().find('.spec_values').val(spec_values.substring(0, spec_values.lastIndexOf(',')));

                var spec_value_ids = '';
                add_spec_value.parent().parent().find('.goods-sepc-value-id-input').each(function () {
                    spec_value_ids += $(this).val() + ',';
                });
                add_spec_value.parent().find('.spec_value_ids').val(spec_value_ids.substring(0, spec_value_ids.lastIndexOf(',')));
                triggerCreateTableBySepc();
            });
        }

        // 显示或隐藏 规格值删除按钮
        $(document).on('mouseenter', '.goods-spec-value', function () {
            $(this).find('.goods-spec-value-del-x').show();
        });

        $(document).on('mouseleave', '.goods-spec-value', function () {
            $(this).find('.goods-spec-value-del-x').hide();
        });

        //删除规格值
        $(document).on('click', '.goods-spec-value-del-x', function () {
            var add_spec_value = $(this).parent().parent().find('.add-spec-value').first();
            $(this).parent().remove();
            specValueLater();
            triggerCreateTableBySepc();
        });

        // 监听规格明细输入，规格数据本地保存
        $(document).on('input', '#more-spec-lists-table input', function () {
            var key = $(this).attr('name') + $(this).parent().parent().attr('spec-value-temp-ids');
            spec_table_data[key] = $(this).val();
        });

        //批量填充
        $(document).on('click', '.batch-spec-content', function () {
            var title = $(this).text();
            var input_name = $(this).attr('input-name');
            layer.prompt({
                formType: 3
                , title: '批量填写' + title
            }, function (value, index, elem) {
                $('input[name="' + input_name + '[]"]').val(value);
                //保存值到本地
                $('#more-spec-lists-table input').each(function () {
                    var key = $(this).attr('name') + $(this).parent().parent().attr('spec-value-temp-ids');
                    spec_table_data[key] = $(this).val();
                });
                layer.close(index);
            });
        });
        //------------------------------------------规格型号 end ------------------------------------

        //------------------------------------------富文本编辑器 begin --------------------------------
        layEditor.set({
            uploadImage: {
                url: '{:url("file/lists")}?type=10'
            },
        })
        var ieditor = layEditor.build('content')
        form.verify({
            content: function(value) {
                return layEditor.sync(ieditor);
            }
        });
        //------------------------------------------富文本编辑器 end --------------------------------

        //------------------------------------------商品类型 begin --------------------------------
        // 监听商品类型radio
        form.on('radio(goods_type)', function (data) {
            switchGoodsType(data.value);
            initDeliveryType(data.value);
        });

        // 切换商品类型
        function switchGoodsType(type) {
            if(type == '1') {
                // 虚拟商品
                $('.virtual-goods-data').show();
                $('.actual-goods-data').hide();
                // 配送方式
                $('.delivery_virtual').show();
                $('.delivery_express').hide();
            } else {
                // 实物商品
                $('.virtual-goods-data').hide();
                $('.actual-goods-data').show();
                // 配送方式
                $('.delivery_virtual').hide();
                $('.delivery_express').show();
            }
        }

        // 初始选中配送方式
        function initDeliveryType(type) {
            // type 0-实物商品 1-虚拟商品
            if(type == '1') {
                $('input[name="delivery_type[]"][value=1]').prop("checked", false);
                $('input[name="delivery_type[]"][value=2]').prop("checked", true);
            } else {
                $('input[name="delivery_type[]"][value=1]').prop("checked", true);
                $('input[name="delivery_type[]"][value=2]').prop("checked", false);
            }
            form.render();
        }

        // 渲染
        function renderDeliveryType(delivery_type, goods_type) {
            delivery_type = delivery_type.split(',');
            if (delivery_type == 'null' || delivery_type == '' ||  delivery_type.length <= 0) {
                return initDeliveryType(goods_type);
            }
            $('input[name="delivery_type[]"]').prop("checked", false);
            for (var i = 0; i < delivery_type.length; i++) {
                $('input[name="delivery_type[]"][value=' + delivery_type[i] + ']').prop("checked", true);
            }
            form.render();
        }

        //------------------------------------------商品类型 end --------------------------------

        //------------------------------------ -----编辑页面 begin --------------------------------
        {notempty name='info'}
        var goods_info= {$info|raw|default=''};

        // 商品类型
        $("input[name=type]").prop("disabled",true);
        $("input[name=type][value="+goods_info['base']['type']+"]").prop("checked",true);
        switchGoodsType(goods_info['base']['type']);
        // 买家付款后 发货后 发货内容
        $("input[name=after_pay][value="+goods_info['base']['after_pay']+"]").prop("checked",true);
        $("input[name=after_delivery][value="+goods_info['base']['after_delivery']+"]").prop("checked",true);
        $('textarea[name="delivery_content"]').val(goods_info['base']['delivery_content']);
        // 配送方式
        renderDeliveryType(goods_info['base']['delivery_type'], goods_info['base']['type']);

        $('input[name="goods_id"]').val(goods_info['base']['id']);
        $('input[name="name"]').val(goods_info['base']['name']);
        $('input[name="code"]').val(goods_info['base']['code']);
        setSelectFirst(goods_info['base']['first_cate_id']);
        setSelectSecond(goods_info['base']['second_cate_id'], goods_info['base']['first_cate_id']);
        setSelectThird(goods_info['base']['third_cate_id'], goods_info['base']['second_cate_id']);
        like.setSelect(goods_info['base']['shop_cate_id'], shop_category_lists, "shop_cate_id", '分类');
        $('input[name="remark"]').val(goods_info['base']['remark']);
        like.setSelect(goods_info['base']['unit_id'], unit, "unit_id", '单位');
        like.setSelect(goods_info['base']['brand_id'], brands, "brand_id", '品牌');
        like.setSelect(goods_info['base']['supplier_id'], supplier, "supplier_id", '供应商');

        //渲染商品主图
        if(goods_info['base']['image']){
            var html = '' +
                '<div class="upload-image-div">' +
                '<img src="' + goods_info['base']['image'] + '" alt="img" />' +
                '<input type="hidden" name="image" value="' + goods_info['base']['image'] + '">' +
                '<div class="del-upload-btn">x</div>' +
                '</div>' +
                '<div class="upload-image-elem" style="display:none;"><a class="add-upload-image" id="image"> + 添加图片</a></div>';
            $('#imageContainer').html(html);
        }

        //渲染分享海报
        if(goods_info['base']['poster']){
            var html = '' +
                '<div class="upload-image-div">' +
                '<img src="' + goods_info['base']['poster'] + '" alt="img" />' +
                '<input type="hidden" name="poster" value="' + goods_info['base']['poster'] + '">' +
                '<div class="del-upload-btn">x</div>' +
                '</div>' +
                '<div class="upload-image-elem" style="display:none;"><a class="add-upload-image" id="poster"> + 添加图片</a></div>';
            $('#posterContainer').html(html);
        }

        // 渲染视频
        if(goods_info['base']['video']){
            var html = '' +
                '<div class="upload-video-div">' +
                '<video src="' + goods_info['base']['video'] + '"></video>' +
                '<input type="hidden" name="video" value="' + goods_info['base']['video'] + '">' +
                '<div class="del-upload-btn">x</div>' +
                '</div>' +
                '<div class="upload-image-elem" style="display:none;"><a class="add-upload-video" id="video"> + 添加视频</a></div>';
            $('#videoContainer').html(html);
        }

        //渲染商品轮播图
        if(goods_info['base']['goods_image']){
            var html = '';
            for(j = 0; j < goods_info['base']['goods_image'].length; j++) {
                html = html +
                    '<div class="upload-image-div">' +
                    '<img src="' + goods_info['base']['goods_image'][j]['abs_image'] + '" alt="img" />' +
                    '<input type="hidden" name="goods_image[]" value="' + goods_info['base']['goods_image'][j]['abs_image'] + '">' +
                    '<div class="del-upload-btn">x</div>' +
                    '</div>';
            }
            html = html + '<div class="upload-image-elem"><a class="add-upload-image" id="goodsimage"> + 添加图片</a></div>';
            $('#goodsImageContainer').html(html);
        }

        // 规格类型
        $("input[name=spec_type][value="+goods_info['base']['spec_type']+"]").prop('checked',"true");
        $("input[name=join_jc][value="+goods_info['base']['join_jc']+"]").prop('checked',"true");

        $('input[name="stock_warn"]').val(goods_info['base']['stock_warn']);
        $("input[name=is_show_stock][value="+goods_info['base']['is_show_stock']+"]").prop("checked",true);  //是否显示库存
        $("input[name=express_type][value="+goods_info['base']['express_type']+"]").prop("checked",true);
        $('input[name="express_money"]').val(goods_info['base']['express_money']);
        like.setSelect(goods_info['base']['express_template_id'], freight, "express_template_id", '运费模板');
        $("input[name=is_member][value="+goods_info['base']['is_member']+"]").prop("checked",true);   //会员价是否开启
        $('input[name="sort"]').val(goods_info['base']['sort']);  //商品排序
        $("input[name=is_recommend][value="+goods_info['base']['is_recommend']+"]").prop("checked",true); // 是否推荐
        $("input[name=status][value="+goods_info['base']['status']+"]").prop("checked",true);   //销售状态

        $("input[name=is_distribution][value="+goods_info['base']['is_distribution']+"]").prop("checked",true);
        $('input[name="first_ratio"]').val(goods_info['base']['first_ratio']);  //一级分销
        $('input[name="second_ratio"]').val(goods_info['base']['second_ratio']); //二级分销
        $('input[name="third_ratio"]').val(goods_info['base']['third_ratio']); //三级分销

        form.render();

        switchSpecType(goods_info['base']['spec_type']);

        if(goods_info['base']['spec_type'] == 1){ // 单规格
            var html = '' +
                '<div class="upload-image-div">' +
                '<img src="' + goods_info['item'][0]['image'] + '" alt="img" />' +
                '<input type="hidden" name="one_spec_image" value="' + goods_info['item'][0]['image'] + '">' +
                '<div class="del-upload-btn">x</div>' +
                '</div>' +
                '<div class="upload-image-elem" style="display:none;"><a class="add-upload-image" id="one_spec_image"> + 添加图片</a></div>';
            if(goods_info['item'][0]['image']){
                $('#one_spec_image').parent().parent().html(html);
            }
            $('input[name="one_market_price"]').val(goods_info['item'][0]['market_price']);
            $('input[name="one_price"]').val(goods_info['item'][0]['price']);
            $('input[name="one_chengben_price"]').val(goods_info['item'][0]['chengben_price']);
            $('input[name="one_stock"]').val(goods_info['item'][0]['stock']);
            $('input[name="one_volume"]').val(goods_info['item'][0]['volume']);
            $('input[name="one_weight"]').val(goods_info['item'][0]['weight']);
            $('input[name="one_bar_code"]').val(goods_info['item'][0]['bar_code']);
        }
        if(goods_info['base']['spec_type'] == 2) { // 多规格
            for(var i in goods_info['spec']){
                addSpec(goods_info['spec'][i]['name']);
                var spes_values = goods_info['spec'][i]['values'];
                for(var j in  spes_values){
                    addSpecvalue($('.add-spec-value').eq(i),spes_values[j]['value'],spes_values[j]['id']);
                }

            }
            for(var i in goods_info['spec']){
                $('input[name="spec_id[]"]').eq(i).val(goods_info['spec'][i]['id']);
            }
            specValueLater();
            createTableBySepc();
            for(var i in goods_info['item']){
                $('#more-spec-lists-table tbody tr').each(function() {
                    var spec_value_str = $(this).find('input[name="spec_value_str[]"]').first().val();
                    if(spec_value_str == goods_info['item'][i]['spec_value_str']){
                        spec_value_temp_ids = $(this).attr('spec-value-temp-ids');
                        spec_table_data["spec_image[]"+spec_value_temp_ids] = goods_info['item'][i]['abs_image'];
                        spec_table_data["price[]"+spec_value_temp_ids] = goods_info['item'][i]['price'];
                        spec_table_data["chengben_price[]"+spec_value_temp_ids] = goods_info['item'][i]['chengben_price'];
                        spec_table_data["market_price[]"+spec_value_temp_ids] = goods_info['item'][i]['market_price'];
                        spec_table_data["pdjc_price[]"+spec_value_temp_ids] = goods_info['item'][i]['pdjc_price'];
                        spec_table_data["stock[]"+spec_value_temp_ids] = goods_info['item'][i]['stock'];
                        spec_table_data["volume[]"+spec_value_temp_ids] = goods_info['item'][i]['volume'];
                        spec_table_data["weight[]"+spec_value_temp_ids] = goods_info['item'][i]['weight'];
                        spec_table_data["bar_code[]"+spec_value_temp_ids] = goods_info['item'][i]['bar_code'];
                        spec_table_data["item_id[]"+spec_value_temp_ids] = goods_info['item'][i]['id'];
                        spec_table_data["spec_value_str[]"+spec_value_temp_ids] = goods_info['item'][i]['spec_value_str'];
                        return false;
                    }
                });
            }
            setTableValue();
        }
        layEditor.setContent(ieditor,goods_info['base']['content']);
        form.render();
        {/notempty}
            //-----------------------------------------编辑页面 end --------------------------------

        // ----------------------------------------资质检查功能 begin ---------------------------
        var qualificationCheckPassed = true; // 资质检查是否通过

        // 检查DOM元素是否存在
        console.log('检查资质相关DOM元素:');
        console.log('qualification-alert:', $('#qualification-alert').length);
        console.log('qualification-message:', $('#qualification-message').length);
        console.log('qualification-actions:', $('#qualification-actions').length);

        // 编辑页面初始化时检查资质（延迟执行确保分类选择器已渲染）
        // 检查是否有商品ID来判断是否为编辑页面
        setTimeout(function() {
            var goodsId = $('input[name="goods_id"]').val();
            if (goodsId && goodsId !== '') {
                console.log('编辑页面初始化，开始检查分类资质');
                checkCategoryQualifications();
            }
        }, 500);

        // 检查分类资质要求
        function checkCategoryQualifications() {
            var categoryId = getCurrentCategoryId();
            if (!categoryId) {
                hideQualificationAlert();
                qualificationCheckPassed = true;
                return;
            }

            // 同时调用两个接口：简单检查和获取资质包信息
            var checkPromise = $.post('{:url("shop_qualification/checkCategoryQualifications")}', {
                category_id: categoryId
            });

            var auditPromise = $.get('{:url("goods.goods/getQualificationAudit")}', {
                category_id: categoryId,
                goods_id: $('input[name="goods_id"]').val() || 0
            });

            // 等待两个请求都完成
            $.when(checkPromise, auditPromise).done(function(checkResult, auditResult) {
                var checkData = checkResult[0];
                var auditData = auditResult[0];

                console.log('资质检查响应:', checkData);
                console.log('资质包数据:', auditData);

                if (checkData.code === 1) {
                    var data = checkData.data;
                    var packageData = (auditData.code === 1) ? auditData.data : [];

                    // 只有必传资质未满足时才阻止提交
                    if (data.missing.length > 0 || data.expired.length > 0) {
                        showQualificationAlert(data, packageData);
                        qualificationCheckPassed = false;
                    } else {
                        // 如果有可选资质缺失，显示提醒但不阻止提交
                        if (data.optional_missing && data.optional_missing.length > 0) {
                            showQualificationAlert(data, packageData);
                        } else {
                            // 即使没有缺失资质，也显示资质包信息
                            showQualificationAlert(data, packageData);
                        }
                        qualificationCheckPassed = true;
                    }
                } else {
                    console.error('资质检查失败:', checkData.msg);
                    hideQualificationAlert();
                    qualificationCheckPassed = true;
                }
            }).fail(function() {
                console.error('资质检查请求失败');
                hideQualificationAlert();
                qualificationCheckPassed = true;
            });
        }

        // 获取当前选中的分类ID（优先选择最深层级的分类）
        function getCurrentCategoryId() {
            var thirdCateId = $('select[name="third_cate_id"]').val();
            var secondCateId = $('select[name="second_cate_id"]').val();
            var firstCateId = $('select[name="first_cate_id"]').val();

            return thirdCateId || secondCateId || firstCateId || '';
        }

        // 显示资质提醒
        function showQualificationAlert(data, packageData) {
            console.log('显示资质提醒:', data);
            console.log('资质包数据:', packageData);

            var message = '<div style="margin-bottom: 16px;"><h4 style="color: #1890ff; font-size: 16px; margin: 0; display: flex; align-items: center;"><i class="layui-icon layui-icon-survey" style="margin-right: 8px; font-size: 18px;"></i>商品资质要求</h4></div>';
            var actions = '';
            var hasRequired = false;
            var displayedQualificationIds = []; // 记录已显示的资质ID，避免重复

            // 首先显示资质包信息
            if (packageData && packageData.length > 0) {
                packageData.forEach(function(package) {
                    message += createPackageCard(package, displayedQualificationIds);
                });
            }

            // 处理独立资质（排除已在资质包中显示的）
            var independentQualifications = [];

            // 收集必传缺失资质
            if (data.missing.length > 0) {
                data.missing.forEach(function(item) {
                    if (displayedQualificationIds.indexOf(item.id) === -1) {
                        item.urgency = 'required';
                        item.statusText = '缺失';
                        independentQualifications.push(item);
                        hasRequired = true;
                    }
                });
            }

            // 收集必传过期资质
            if (data.expired.length > 0) {
                data.expired.forEach(function(item) {
                    if (displayedQualificationIds.indexOf(item.id) === -1) {
                        item.urgency = 'expired';
                        item.statusText = '已过期';
                        independentQualifications.push(item);
                        hasRequired = true;
                    }
                });
            }

            // 收集可选资质
            if (data.optional_missing && data.optional_missing.length > 0) {
                data.optional_missing.forEach(function(item) {
                    if (displayedQualificationIds.indexOf(item.id) === -1) {
                        item.urgency = 'optional';
                        item.statusText = '建议上传';
                        independentQualifications.push(item);
                    }
                });
            }

            // 显示独立资质
            if (independentQualifications.length > 0) {
                message += createIndependentQualificationsSection(independentQualifications);
            }

            // 显示资质包中的独立资质（selection_mode = 3）
            if (packageData && packageData.length > 0) {
                packageData.forEach(function(package) {
                    if (package.selection_mode == 3) { // 独立资质包
                        // 这些资质已经在资质包中显示了，不需要重复处理
                    }
                });
            }

            // 添加底部提示
            message += createStatusSummary(hasRequired, packageData, independentQualifications);

            console.log('设置资质消息:', message);
            console.log('设置资质操作:', actions);
            $('#qualification-message').html(message);
            $('#qualification-actions').html('<div class="qualification-actions">' + actions + '</div>');
            $('#qualification-alert').show();
            console.log('显示资质提醒区域');
        }

        // 创建资质项目HTML
        function createQualificationItem(item, type, status) {
            var badgeClass = type === 'required' || type === 'expired' ? 'required' : 'optional';
            var badgeText = type === 'required' || type === 'expired' ? '必传' : '可选';
            var statusText = item.reason ? status + ' (' + item.reason + ')' : status;

            var templateHtml = '';
            if (item.document_path && item.document_name) {
                templateHtml = '<div class="qualification-template">' +
                    '<a href="' + item.document_path + '" target="_blank" class="qualification-template-btn">' +
                    '<i class="layui-icon layui-icon-download-circle"></i>下载承诺书模板' +
                    '</a>' +
                    '</div>';
            }

            // 示例按钮HTML
            var exampleHtml = '';
            if (item.ex_img) {
                exampleHtml = '<button class="qualification-example-btn" onclick="showExampleImage(\'' + item.ex_img + '\', \'' + item.name + '\')">' +
                    '<i class="layui-icon layui-icon-picture"></i>示例' +
                    '</button>';
            }

            return '<div class="qualification-item ' + type + '">' +
                '<div class="qualification-header">' +
                '<span class="qualification-title">' + item.name + '</span>' +
                '<span class="qualification-badge ' + badgeClass + '">' + badgeText + '</span>' +
                exampleHtml +
                '<span style="margin-left: 8px; color: #999; font-size: 12px;">' + statusText + '</span>' +
                '</div>' +
                (item.description ? '<div class="qualification-description">' + item.description + '</div>' : '') +
                templateHtml +
                '</div>';
        }

        // 创建上传按钮HTML
        function createUploadButton(item, type, text) {
            var buttonClass = type === 'required' ? 'required' : 'optional';
            return '<button type="button" class="qualification-upload-btn ' + buttonClass + '" onclick="uploadQualification(' + item.id + ', \'' + item.name + '\')">' +
                '<i class="layui-icon layui-icon-upload" style="margin-right: 4px;"></i>' + text + ' ' + item.name +
                '</button>';
        }

        // 隐藏资质提醒
        function hideQualificationAlert() {
            $('#qualification-alert').hide();
        }

        // 资质文件上传事件处理
        $(document).on('click', '.qualification-upload-trigger', function() {
            var qualificationId = $(this).data('qualification-id');
            var qualificationName = $(this).data('qualification-name');
            var that = $(this);

            like.imageUpload({
                limit: 1,
                field: "qualification_document",
                that: that,
                content: '/shop/file/lists?type=10',
                success: function(data) {
                    // 上传成功后，提交资质信息
                    if (data && data.length > 0) {
                        var fileInfo = data[0];
                        submitQualificationDocument(qualificationId, qualificationName, fileInfo.src, fileInfo.name);
                    }
                }
            });
        });

        // 提交资质文档
        function submitQualificationDocument(qualificationId, qualificationName, filePath, fileName) {
            var goodsId = $('input[name="goods_id"]').val() || 0;
            var useCustomQualification = $('input[name="use_custom_qualification"]:checked').val() || 0;

            // 如果商品使用独立资质，则传递商品ID，否则传递0（通用资质）
            var targetGoodsId = (useCustomQualification == 1 && goodsId > 0) ? goodsId : 0;

            $.post('{:url("shop_qualification/quickUpload")}', {
                qualification_id: qualificationId,
                goods_id: targetGoodsId,
                document_path: filePath,
                document_name: fileName || qualificationName + '资质文档'
            }, function(res) {
                if (res.code === 1) {
                    layer.msg('资质上传成功', {icon: 1});
                    // 重新检查资质
                    checkCategoryQualifications();
                } else {
                    layer.msg(res.msg || '上传失败', {icon: 2});
                }
            }).fail(function() {
                layer.msg('上传失败，请重试', {icon: 2});
            });
        }

        // 保留原有的上传资质函数（兼容性）
        window.uploadQualification = function(qualificationId, qualificationName) {
            layer.open({
                type: 2,
                title: '上传资质：' + qualificationName,
                area: ['800px', '600px'],
                content: '{:url("shop_qualification/quickUpload")}?qualification_id=' + qualificationId + '&qualification_name=' + encodeURIComponent(qualificationName),
                end: function() {
                    // 重新检查资质
                    checkCategoryQualifications();
                }
            });
        };

        // 刷新资质检查（供子窗口调用）
        window.refreshQualificationCheck = function() {
            checkCategoryQualifications();
        };

        // 显示示例图片
        window.showExampleImage = function(imagePath, qualificationName) {
            if (!imagePath) {
                layer.msg('暂无示例图片', { icon: 2 });
                return;
            }

            // 使用layer.open显示图片，控制大小
            layer.open({
                type: 1,
                title: qualificationName + ' - 示例图片',
                area: ['600px', '450px'],
                maxmin: true,
                shadeClose: true,
                content: '<div style="text-align: center; padding: 20px;"><img src="' + imagePath + '" style="max-width: 100%; max-height: 100%; object-fit: contain;" alt="示例图片"></div>'
            });
        };

        // 在表单提交前检查资质
        form.on('submit(goods-submit)', function(data) {
            if (!qualificationCheckPassed) {
                layer.msg('请先上传所需的资质文档', {icon: 2});
                return false;
            }
            // 如果资质检查通过，继续原有的提交逻辑
            return true;
        });
        // 创建资质包卡片
        function createPackageCard(package, displayedQualificationIds) {
            var html = '';
            var packageStatusSummary = getPackageStatusSummary(package);

            html += '<div class="qualification-package-card" style="margin-bottom: 16px; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1); background: #fff;">';

            // 包头部
            html += '<div class="package-header" style="background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%); padding: 16px; color: white;">';
            html += '<div style="display: flex; justify-content: space-between; align-items: center;">';
            html += '<div>';
            html += '<h5 style="margin: 0; font-size: 16px; font-weight: 600;"><i class="layui-icon layui-icon-component" style="margin-right: 8px;"></i>' + package.package_name + '</h5>';
            html += '<div style="margin-top: 6px; display: flex; align-items: center; gap: 8px;">';

            // 突出显示选择模式
            var modeColor, modeText;
            if (package.selection_mode == 1) {
                modeColor = '#ff4d4f';
                modeText = '全部需要';
            } else if (package.selection_mode == 2) {
                modeColor = '#52c41a';
                modeText = '任选一个';
            } else if (package.selection_mode == 3) {
                // 独立资质的混合模式
                modeColor = '#faad14';
                modeText = package.selection_mode_text;
            } else {
                modeColor = '#1890ff';
                modeText = package.selection_mode_text;
            }
            html += '<span style="background: rgba(255,255,255,0.9); color: ' + modeColor + '; padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: 700; border: 1px solid rgba(255,255,255,0.5);">';
            html += modeText;
            html += '</span>';

            html += '<span style="font-size: 11px; opacity: 0.8;">' + package.selection_mode_desc + '</span>';
            html += '</div>';
            html += '</div>';
            html += '<div class="package-status-badge" style="background: rgba(255,255,255,0.2); padding: 6px 12px; border-radius: 20px; font-size: 12px;">';
            html += packageStatusSummary.text;
            html += '</div>';
            html += '</div>';
            html += '</div>';

            // 包内容
            html += '<div class="package-content" style="padding: 16px;">';

            if (package.qualifications && package.qualifications.length > 0) {
                html += '<div class="qualification-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 12px;">';

                package.qualifications.forEach(function(qual) {
                    displayedQualificationIds.push(qual.id);
                    html += createQualificationCard(qual, false);
                });

                html += '</div>';
            } else {
                html += '<div style="text-align: center; color: #999; padding: 20px;">该资质包暂无资质项目</div>';
            }

            html += '</div>';
            html += '</div>';

            return html;
        }

        // 创建资质卡片
        function createQualificationCard(qual, isIndependent, urgency) {
            var statusInfo = getQualificationStatusInfo(qual);
            var html = '';

            html += '<div class="qualification-card ' + statusInfo.class + '" style="border: 1px solid ' + statusInfo.borderColor + '; border-radius: 6px; padding: 16px; background: ' + statusInfo.bgColor + '; transition: all 0.3s ease;">';

            // 资质头部
            html += '<div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 12px;">';
            html += '<div style="flex: 1;">';
            html += '<div style="display: flex; align-items: center; gap: 8px; margin-bottom: 4px; flex-wrap: wrap;">';
            html += '<h6 style="margin: 0; font-size: 14px; font-weight: 600; color: #333;">' + qual.name + '</h6>';

            // 示例按钮放在名称后面
            if (qual.ex_img) {
                html += '<button type="button" class="example-btn-inline" onclick="showExampleImage(\'' + qual.ex_img + '\', \'' + qual.name + '\')" ';
                html += 'style="padding: 2px 6px; border: 1px solid #52c41a; border-radius: 3px; background: #f6ffed; color: #52c41a; font-size: 10px; cursor: pointer; transition: all 0.3s;">';
                html += '<i class="layui-icon layui-icon-picture" style="margin-right: 2px; font-size: 10px;"></i>示例';
                html += '</button>';
            }

            // 显示必传/选传标识
            var urgencyBadge = '';
            if (isIndependent) {
                // 独立资质根据urgency参数显示
                if (urgency === 'required' || urgency === 'expired') {
                    urgencyBadge = '<span style="padding: 2px 6px; border-radius: 3px; background: #ff4d4f; color: white; font-size: 10px; font-weight: 600;">必传</span>';
                } else if (urgency === 'optional') {
                    urgencyBadge = '<span style="padding: 2px 6px; border-radius: 3px; background: #faad14; color: white; font-size: 10px; font-weight: 600;">选传</span>';
                }
            } else {
                // 资质包中的资质根据is_required字段显示
                if (qual.is_required == 1) {
                    urgencyBadge = '<span style="padding: 2px 6px; border-radius: 3px; background: #ff4d4f; color: white; font-size: 10px; font-weight: 600;">必传</span>';
                } else {
                    urgencyBadge = '<span style="padding: 2px 6px; border-radius: 3px; background: #faad14; color: white; font-size: 10px; font-weight: 600;">选传</span>';
                }
            }
            html += urgencyBadge;

            html += '</div>';
            if (qual.description) {
                html += '<p style="margin: 0; font-size: 12px; color: #666; line-height: 1.4;">' + qual.description + '</p>';
            }
            html += '</div>';
            html += '<div class="status-indicator" style="margin-left: 12px;">';
            html += '<span style="display: inline-flex; align-items: center; padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: 600; background: ' + statusInfo.statusBg + '; color: ' + statusInfo.statusColor + ';">';
            html += statusInfo.icon + statusInfo.text;
            html += '</span>';
            html += '</div>';
            html += '</div>';

            // 文档预览区域
            if (qual.document_path) {
                html += '<div class="document-preview" style="margin-bottom: 12px; padding: 8px; background: #f8f9fa; border-radius: 4px; border: 1px dashed #d9d9d9;">';
                html += '<div style="display: flex; align-items: center; gap: 8px;">';
                html += '<img src="' + qual.document_path + '" style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px; cursor: pointer;" onclick="previewDocument(\'' + qual.document_path + '\', \'' + qual.name + '\')">';
                html += '<div style="flex: 1;">';
                html += '<div style="font-size: 12px; font-weight: 600; color: #333;">' + (qual.document_name || '资质文件') + '</div>';
                html += '<div style="font-size: 11px; color: #666;">' + (qual.is_goods_specific ? '商品专用资质' : '通用资质') + '</div>';
                html += '</div>';
                html += '</div>';
                html += '</div>';
            }

            // 操作按钮区域
            html += '<div class="action-buttons" style="display: flex; gap: 8px; align-items: center;">';

            // 上传按钮 - 使用系统文件上传组件
            if (statusInfo.needUpload) {
                var uploadId = 'qualification_upload_' + qual.id;
                html += '<div class="qualification-upload-container" style="flex: 1;">';
                html += '<button type="button" id="' + uploadId + '" class="upload-btn qualification-upload-trigger" data-qualification-id="' + qual.id + '" data-qualification-name="' + qual.name + '" ';
                html += 'style="width: 100%; padding: 8px 16px; border: none; border-radius: 4px; font-size: 12px; font-weight: 600; cursor: pointer; transition: all 0.3s; ';
                html += 'background: ' + (statusInfo.urgent ? '#ff4d4f' : '#1890ff') + '; color: white;">';
                html += '<i class="layui-icon layui-icon-upload" style="margin-right: 4px;"></i>' + statusInfo.buttonText;
                html += '</button>';
                html += '</div>';
            }

            html += '</div>';

            // 审核备注
            if (qual.audit_remark) {
                html += '<div style="margin-top: 8px; padding: 8px; background: #fff2f0; border: 1px solid #ffccc7; border-radius: 4px;">';
                html += '<div style="font-size: 11px; color: #ff4d4f; font-weight: 600;">审核意见：</div>';
                html += '<div style="font-size: 12px; color: #ff4d4f; margin-top: 2px;">' + qual.audit_remark + '</div>';
                html += '</div>';
            }

            html += '</div>';

            return html;
        }

        // 获取资质包状态摘要
        function getPackageStatusSummary(package) {
            if (!package.qualifications || package.qualifications.length === 0) {
                return { text: '无资质项目', color: '#999' };
            }

            var total = package.qualifications.length;
            var passed = 0;
            var pending = 0;
            var failed = 0;
            var notUploaded = 0;

            package.qualifications.forEach(function(qual) {
                if (qual.status == 1) {
                    passed++;
                } else if (qual.status == 2) {
                    failed++;
                } else if (qual.shop_qualification_id > 0) {
                    pending++;
                } else {
                    notUploaded++;
                }
            });

            if (passed === total) {
                return { text: '全部通过', color: '#52c41a' };
            } else if (notUploaded > 0 || failed > 0) {
                return { text: (notUploaded + failed) + '/' + total + ' 待处理', color: '#ff4d4f' };
            } else {
                return { text: pending + '/' + total + ' 待审核', color: '#faad14' };
            }
        }

        // 获取资质状态信息
        function getQualificationStatusInfo(qual) {
            var info = {
                class: '',
                borderColor: '#e6e6e6',
                bgColor: '#fff',
                statusBg: '#f0f0f0',
                statusColor: '#666',
                icon: '',
                text: '',
                needUpload: false,
                buttonText: '',
                urgent: false
            };

            if (qual.status == 1) {
                // 已通过
                info.class = 'status-passed';
                info.borderColor = '#52c41a';
                info.bgColor = '#f6ffed';
                info.statusBg = '#52c41a';
                info.statusColor = '#fff';
                info.icon = '<i class="layui-icon layui-icon-ok" style="margin-right: 4px;"></i>';
                info.text = '已通过';
            } else if (qual.status == 2) {
                // 审核未通过
                info.class = 'status-failed';
                info.borderColor = '#ff4d4f';
                info.bgColor = '#fff2f0';
                info.statusBg = '#ff4d4f';
                info.statusColor = '#fff';
                info.icon = '<i class="layui-icon layui-icon-close" style="margin-right: 4px;"></i>';
                info.text = '未通过';
                info.needUpload = true;
                info.buttonText = '重新上传';
                info.urgent = true;
            } else if (qual.shop_qualification_id > 0) {
                // 待审核
                info.class = 'status-pending';
                info.borderColor = '#faad14';
                info.bgColor = '#fffbe6';
                info.statusBg = '#faad14';
                info.statusColor = '#fff';
                info.icon = '<i class="layui-icon layui-icon-time" style="margin-right: 4px;"></i>';
                info.text = '待审核';
            } else {
                // 未上传
                info.class = 'status-not-uploaded';
                info.borderColor = '#d9d9d9';
                info.bgColor = '#fafafa';
                info.statusBg = '#d9d9d9';
                info.statusColor = '#666';
                info.icon = '<i class="layui-icon layui-icon-upload" style="margin-right: 4px;"></i>';
                info.text = '未上传';
                info.needUpload = true;
                info.buttonText = '上传资质';
            }

            return info;
        }

        // 创建独立资质区域
        function createIndependentQualificationsSection(qualifications) {
            var html = '';

            html += '<div class="independent-qualifications" style="margin-top: 16px;">';
            html += '<div style="margin-bottom: 12px; padding: 12px; background: #f8f9fa; border-radius: 6px; border-left: 4px solid #6c757d;">';
            html += '<h6 style="margin: 0; color: #495057; font-size: 14px;"><i class="layui-icon layui-icon-file" style="margin-right: 8px;"></i>独立资质要求</h6>';
            html += '</div>';

            html += '<div class="qualification-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 12px;">';

            qualifications.forEach(function(qual) {
                // 转换为标准格式
                var standardQual = {
                    id: qual.id,
                    name: qual.name,
                    description: qual.description || '',
                    status: 0, // 独立资质默认未上传
                    shop_qualification_id: 0,
                    document_path: '',
                    document_name: '',
                    ex_img: qual.ex_img || '',
                    audit_remark: '',
                    is_goods_specific: false,
                    is_required: qual.is_required || 0
                };

                // 根据urgency设置状态
                if (qual.urgency === 'expired') {
                    standardQual.status = 2; // 设为审核未通过，触发重新上传
                }

                // 根据is_required字段确定urgency（如果没有从原始数据传递）
                var urgency = qual.urgency;
                if (!urgency) {
                    urgency = (qual.is_required == 1) ? 'required' : 'optional';
                }

                html += createQualificationCard(standardQual, true, urgency);
            });

            html += '</div>';
            html += '</div>';

            return html;
        }

        // 创建状态摘要
        function createStatusSummary(hasRequired, packageData, independentQualifications) {
            var html = '';
            var totalRequired = 0;
            var completedRequired = 0;

            // 统计资质包中的必传资质
            if (packageData && packageData.length > 0) {
                packageData.forEach(function(package) {
                    if (package.qualifications) {
                        package.qualifications.forEach(function(qual) {
                            if (package.selection_mode == 1) { // 全部需要
                                totalRequired++;
                                if (qual.status == 1) completedRequired++;
                            }
                        });
                    }
                });
            }

            // 统计独立必传资质
            if (independentQualifications) {
                independentQualifications.forEach(function(qual) {
                    if (qual.urgency === 'required' || qual.urgency === 'expired') {
                        totalRequired++;
                        // 独立资质在这里都是未完成的
                    }
                });
            }

            html += '<div style="margin-top: 20px; padding: 16px; border-radius: 8px; ';

            if (hasRequired) {
                html += 'background: linear-gradient(135deg, #fff2f0 0%, #ffebe8 100%); border: 1px solid #ffccc7;">';
                html += '<div style="display: flex; align-items: center; margin-bottom: 8px;">';
                html += '<i class="layui-icon layui-icon-close-fill" style="color: #ff4d4f; font-size: 18px; margin-right: 8px;"></i>';
                html += '<h6 style="margin: 0; color: #ff4d4f; font-size: 14px; font-weight: 600;">资质审核未完成</h6>';
                html += '</div>';
                html += '<div style="color: #ff4d4f; font-size: 12px; line-height: 1.5;">';
                html += '还有 <strong>' + (totalRequired - completedRequired) + '</strong> 项必传资质需要处理，请上传相关资质文档并等待审核通过后再提交商品。';
                html += '</div>';
            } else {
                html += 'background: linear-gradient(135deg, #f6ffed 0%, #edf9e3 100%); border: 1px solid #b7eb8f;">';
                html += '<div style="display: flex; align-items: center; margin-bottom: 8px;">';
                html += '<i class="layui-icon layui-icon-ok-circle" style="color: #52c41a; font-size: 18px; margin-right: 8px;"></i>';
                html += '<h6 style="margin: 0; color: #52c41a; font-size: 14px; font-weight: 600;">资质审核完成</h6>';
                html += '</div>';
                html += '<div style="color: #52c41a; font-size: 12px; line-height: 1.5;">';
                html += '所有必传资质已满足要求，可以正常提交商品。如有可选资质建议一并上传以提升商品可信度。';
                html += '</div>';
            }

            html += '</div>';

            return html;
        }

        // 预览文档
        window.previewDocument = function(imagePath, qualificationName) {
            layer.open({
                type: 1,
                title: qualificationName + ' - 资质文档',
                area: ['80%', '80%'],
                maxmin: true,
                shadeClose: true,
                content: '<div style="text-align: center; padding: 20px;"><img src="' + imagePath + '" style="max-width: 100%; max-height: 100%; object-fit: contain;" alt="资质文档"></div>'
            });
        };

        // ----------------------------------------资质检查功能 end -----------------------------
    });
</script>