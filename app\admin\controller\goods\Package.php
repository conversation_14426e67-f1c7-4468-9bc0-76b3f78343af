<?php
// +----------------------------------------------------------------------
// | kshop
// +----------------------------------------------------------------------
// | Copyright (c) 2022~2024 kshop All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( https://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: kshop
// +----------------------------------------------------------------------
namespace app\admin\controller\goods;

use app\common\basics\AdminBase;
use app\common\model\goods\Qualification as QualificationModel;
use app\model\goods\QualificationPackage;
use app\model\goods\QualificationPackageItem;
use app\common\server\JsonServer;
use think\facade\Db;
use think\facade\View;

/**
 * 资质包管理
 * Class Package
 * @package app\admin\controller\goods
 */
class Package extends AdminBase
{
    /**
     * 列表
     */
    public function lists()
    {
        if ($this->request->isAjax()) {
            $param = $this->request->get();
            $limit = $param['limit'] ?? 10;
            $page = $param['page'] ?? 1;
            
            $where = [['del', '=', 0]];
            
            // 搜索条件
            if (isset($param['name']) && !empty(trim($param['name']))) {
                $where[] = ['name', 'like', '%' . trim($param['name']) . '%'];
            }
            
            try {
                // 查询总数
                $total = Db::name('qualification_package')->where($where)->count();
                
                // 查询数据
                $packages = Db::name('qualification_package')
                    ->where($where)
                    ->order('sort', 'asc')
                    ->order('id', 'desc')
                    ->limit(($page - 1) * $limit, $limit)
                    ->select();
                
                $result = [];
                foreach ($packages as $package) {
                    // 获取关联的资质
                    $qualifications = Db::name('qualification_package_item')->alias('qpi')
                        ->join('qualification q', 'qpi.qualification_id = q.id')
                        ->where('qpi.package_id', $package['id'])
                        ->where('q.del', 0)
                        ->field('q.id, q.name')
                        ->select();
                    
                    // 获取绑定的分类
                    $boundCategories = Db::name('qualification_package_category')->alias('qpc')
                        ->join('goods_category gc', 'qpc.category_id = gc.id')
                        ->where('qpc.package_id', $package['id'])
                        ->where('gc.del', 0)
                        ->column('gc.name');
                    
                    // 处理选择模式
                    $selectionModeText = $package['selection_mode'] == 1 ? '全部需要' : '任选其一';
                    
                    $result[] = [
                        'id' => $package['id'],
                        'name' => $package['name'],
                        'selection_mode' => [
                            'text' => $selectionModeText,
                            'value' => $package['selection_mode']
                        ],
                        'status' => $package['status'],
                        'sort' => $package['sort'],
                        'qualifications' => $qualifications ?: [],
                        'bound_categories' => $boundCategories ?: []
                    ];
                }
                return JsonServer::success('',['count' => $total, 'lists' => $result]);
            } catch (\Exception $e) {
                return JsonServer::error('获取失败: ' . $e->getMessage());
            }
        }
        return view();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $param = $this->request->post();
            $qualification_ids = $param['qualification_ids'] ?? [];
            
            if (empty($qualification_ids)) {
                return JsonServer::error('请至少选择一个资质证书');
            }

            Db::startTrans();
            try {
                // 创建资质包
                $package = QualificationPackage::create($param);
                
                // 创建资质包项目关联
                $items = [];
                foreach ($qualification_ids as $id) {
                    $items[] = [
                        'package_id' => $package->id,
                        'qualification_id' => $id
                    ];
                }
                (new QualificationPackageItem())->saveAll($items);
                
                Db::commit();
                return JsonServer::success('添加成功');
            } catch (\Exception $e) {
                Db::rollback();
                return JsonServer::error('添加失败: ' . $e->getMessage());
            }
        }

        // 获取所有启用的资质
        $qualifications = QualificationModel::where('del', 0)
            ->where('status', 1)
            ->order('sort', 'asc')
            ->select();
        
        View::assign('qualifications', $qualifications);
        return view();
    }

    /**
     * 编辑
     */
    public function edit()
    {
        if ($this->request->isPost()) {
            $param = $this->request->post();
            $id = $param['id'];
            $qualification_ids = $param['qualification_ids'] ?? [];
            
            if (empty($qualification_ids)) {
                return JsonServer::error('请至少选择一个资质证书');
            }

            Db::startTrans();
            try {
                $model = new QualificationPackage();
                $info = $model->find($id);
                if (empty($info)) {
                    return JsonServer::error('资质包不存在');
                }
                
                // 更新资质包信息
                $info->save($param);
                
                // 删除原有的资质包项目关联
                QualificationPackageItem::where('package_id', $id)->delete();
                
                // 创建新的资质包项目关联
                $items = [];
                foreach ($qualification_ids as $q_id) {
                    $items[] = [
                        'package_id' => $id,
                        'qualification_id' => $q_id
                    ];
                }
                (new QualificationPackageItem())->saveAll($items);
                
                Db::commit();
                return JsonServer::success('编辑成功');
            } catch (\Exception $e) {
                Db::rollback();
                return JsonServer::error('编辑失败: ' . $e->getMessage());
            }
        } else {
            $id = $this->request->get('id');
            $model = new QualificationPackage();
            $info = $model->with(['items'])->find($id);
           
            if (empty($info)) {
                return view('error/404');
            }
            
            // 获取所有启用的资质
            $qualifications = QualificationModel::where('del', 0)
                ->where('status', 1)
                ->order('sort', 'asc')
                ->select();
            
            // 获取已选择的资质ID
            $checked_ids = array_column($info->items->toArray(), 'qualification_id');
            View::assign('info', $info);
            View::assign('qualifications', $qualifications);
            View::assign('checked_ids', $checked_ids);
            return view();
        }
    }

    /**
     * 删除
     */
    public function del()
    {
        $id = $this->request->post('id');
        $model = new QualificationPackage();
        try {
            $model->find($id)->delete();
            return JsonServer::success('删除成功');
        } catch (\Exception $e) {
            return JsonServer::error('删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 状态切换
     */
    public function status()
    {
        $post = $this->request->post();
        $data = [
            $post['field'] => $post['value'],
            'update_time' => time()
        ];

        $result = QualificationPackage::where('id', $post['id'])->update($data);
        if ($result) {
            return JsonServer::success('操作成功');
        }
        return JsonServer::error('操作失败');
    }

    /**
     * 获取所有启用的资质包（用于其他模块调用）
     */
    public function getEnabled()
    {
        $packages = QualificationPackage::with(['qualifications'])
            ->where(['del' => 0, 'status' => 1])
            ->order('sort', 'asc')
            ->select();

        return JsonServer::success('获取成功', $packages);
    }

    /**
     * 测试数据获取
     */
    public function test()
    {
        try {
            // 直接查询数据库
            $packages = Db::name('qualification_package')
                ->where('del', 0)
                ->limit(5)
                ->select();
            
            $result = [];
            foreach ($packages as $package) {
                // 获取关联的资质
                $qualifications = Db::name('qualification_package_item')->alias('qpi')
                    ->join('qualification q', 'qpi.qualification_id = q.id')
                    ->where('qpi.package_id', $package['id'])
                    ->where('q.del', 0)
                    ->field('q.id, q.name')
                    ->select();
                
                $result[] = [
                    'id' => $package['id'],
                    'name' => $package['name'],
                    'selection_mode' => $package['selection_mode'],
                    'status' => $package['status'],
                    'sort' => $package['sort'],
                    'qualifications' => $qualifications,
                    'bound_categories' => []
                ];
            }
            
            return JsonServer::success('测试成功', [
                'total' => count($result),
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return JsonServer::error('测试失败: ' . $e->getMessage());
        }
    }
/**
     * 获取分类树数据（AJAX）
     */
    public function getPackageCategoryTreeForPackage()
    {
        $qualificationId = $this->request->param('id');

        try {
            // 获取分类树数据
            $categoryTree = $this->getCategoryTreeForPackage($qualificationId);
            return JsonServer::success('获取成功', $categoryTree);
        } catch (\Exception $e) {
            return JsonServer::error('获取失败：' . $e->getMessage());
        }
    }
    /**
     * 绑定分类页面
     */
    public function bindCategories()
    {
        $id = $this->request->get('id');
        $package = QualificationPackage::find($id);

        if (!$package) {
            return JsonServer::error('资质包不存在');
        }

        // 获取分类树数据
        $categoryTree = $this->getCategoryTreeForPackage($id);

        View::assign('qualification', $package);
        View::assign('categoryTree', json_encode($categoryTree, JSON_UNESCAPED_UNICODE));
        return View::fetch('bind_categories');
    }

    /**
     * 获取资质包的分类树数据
     */
    private function getCategoryTreeForPackage($packageId)
    {
        // 获取所有分类
        $categories = \app\common\model\goods\GoodsCategory::where('del', 0)
            ->field('id,name,pid,level')
            ->order('sort', 'asc')
            ->select()
            ->toArray();

        // 获取该资质包绑定的分类ID
        $boundCategoryIds = Db::name('qualification_package_category')
            ->where('package_id', $packageId)
            ->column('category_id');

        // 构建树形结构
        return $this->buildCategoryTree($categories, 0, $boundCategoryIds);
    }

    /**
     * 构建分类树
     */
    private function buildCategoryTree($categories, $pid = 0, $boundIds = [])
    {
        $tree = [];
        foreach ($categories as $category) {
            if ($category['pid'] == $pid) {
                $node = [
                    'id' => $category['id'],
                    'title' => $category['name'],
                    'checked' => in_array($category['id'], $boundIds),
                    'spread' => true,
                ];

                // 递归获取子分类
                $children = $this->buildCategoryTree($categories, $category['id'], $boundIds);
                if (!empty($children)) {
                    $node['children'] = $children;
                }

                $tree[] = $node;
            }
        }
        return $tree;
    }

    /**
     * 保存资质包分类绑定关系
     */
    public function saveBinding()
    {
        $id = $this->request->post('id');
        $categoryIds = $this->request->post('category_ids', []);

        if (!$id) {
            return JsonServer::error('资质包ID不能为空');
        }

        Db::startTrans();
        try {
            // 删除原有绑定关系
            Db::name('qualification_package_category')
                ->where('package_id', $id)
                ->delete();

            // 添加新的绑定关系
            if (!empty($categoryIds)) {
                $data = [];
                foreach ($categoryIds as $categoryId) {
                    $data[] = [
                        'package_id' => $id,
                        'category_id' => $categoryId,
                        'create_time' => time()
                    ];
                }
                Db::name('qualification_package_category')->insertAll($data);
            }

            Db::commit();
            return JsonServer::success('绑定成功');
        } catch (\Exception $e) {
            Db::rollback();
            return JsonServer::error('绑定失败: ' . $e->getMessage());
        }
    }
}
