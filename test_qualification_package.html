<!DOCTYPE html>
<html>
<head>
    <title>测试资质包和资质管理</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        button { margin: 5px; padding: 8px 15px; }
    </style>
</head>
<body>
    <h1>资质管理系统测试</h1>
    
    <div class="test-section">
        <h2>资质包管理测试</h2>
        <button onclick="testPackageList()">测试资质包列表</button>
        <button onclick="testPackageTest()">测试资质包测试接口</button>
        <div id="package-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>资质管理测试</h2>
        <button onclick="testQualificationList()">测试资质列表</button>
        <button onclick="testQualificationTest()">测试资质测试接口</button>
        <div id="qualification-result" class="result"></div>
    </div>

    <script>
        function testPackageList() {
            $.ajax({
                url: '/admin/goods.package/lists',
                type: 'GET',
                data: { page: 1, limit: 5 },
                success: function(res) {
                    console.log('资质包列表响应:', res);
                    $('#package-result').html('<h3>资质包列表结果:</h3><pre>' + JSON.stringify(res, null, 2) + '</pre>');
                },
                error: function(xhr, status, error) {
                    console.error('资质包列表错误:', error);
                    $('#package-result').html('<h3>错误:</h3>' + error);
                }
            });
        }

        function testPackageTest() {
            $.ajax({
                url: '/admin/goods.package/test',
                type: 'GET',
                success: function(res) {
                    console.log('资质包测试响应:', res);
                    $('#package-result').html('<h3>资质包测试结果:</h3><pre>' + JSON.stringify(res, null, 2) + '</pre>');
                },
                error: function(xhr, status, error) {
                    console.error('资质包测试错误:', error);
                    $('#package-result').html('<h3>错误:</h3>' + error);
                }
            });
        }

        function testQualificationList() {
            $.ajax({
                url: '/admin/goods.qualification/lists',
                type: 'GET',
                data: { page: 1, limit: 5 },
                success: function(res) {
                    console.log('资质列表响应:', res);
                    $('#qualification-result').html('<h3>资质列表结果:</h3><pre>' + JSON.stringify(res, null, 2) + '</pre>');
                },
                error: function(xhr, status, error) {
                    console.error('资质列表错误:', error);
                    $('#qualification-result').html('<h3>错误:</h3>' + error);
                }
            });
        }

        function testQualificationTest() {
            $.ajax({
                url: '/admin/goods.qualification/test',
                type: 'GET',
                success: function(res) {
                    console.log('资质测试响应:', res);
                    $('#qualification-result').html('<h3>资质测试结果:</h3><pre>' + JSON.stringify(res, null, 2) + '</pre>');
                },
                error: function(xhr, status, error) {
                    console.error('资质测试错误:', error);
                    $('#qualification-result').html('<h3>错误:</h3>' + error);
                }
            });
        }
    </script>
</body>
</html>