<?php

namespace app\common\model\shop;

use app\common\basics\Models;
use app\common\model\goods\Qualification;

/**
 * 商家资质模型
 * Class ShopQualification
 * @package app\common\model\shop
 */
class ShopQualification extends Models
{
    /**
     * 关联资质表
     */
    public function qualification()
    {
        return $this->belongsTo(Qualification::class, 'qualification_id', 'id');
    }

    /**
     * 关联商家表
     */
    public function shop()
    {
        return $this->belongsTo(\app\common\model\Shop::class, 'shop_id', 'id');
    }

    /**
     * 关联商品表
     */
    public function goods()
    {
        return $this->belongsTo(\app\common\model\goods\Goods::class, 'goods_id', 'id');
    }

    /**
     * 获取状态描述
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = isset($data['status']) ? intval($data['status']) : 0;
        $statusMap = [
            0 => '待审核',
            1 => '审核通过',
            2 => '审核拒绝'
        ];
        return $statusMap[$status] ?? '未知状态';
    }

    /**
     * 获取过期时间描述
     */
    public function getExpireTimeTextAttr($value, $data)
    {
        $expireTime = isset($data['expire_time']) ? intval($data['expire_time']) : 0;
        if ($expireTime == 0) {
            return '永久有效';
        }
        return date('Y-m-d H:i:s', $expireTime);
    }

    /**
     * 检查是否过期
     */
    public function getIsExpiredAttr($value, $data)
    {
        $expireTime = isset($data['expire_time']) ? intval($data['expire_time']) : 0;
        if ($expireTime == 0) {
            return false; // 永久有效
        }
        return time() > $expireTime;
    }

    /**
     * 获取审核时间描述
     */
    public function getAuditTimeTextAttr($value, $data)
    {
        $auditTime = isset($data['audit_time']) ? intval($data['audit_time']) : 0;
        if ($auditTime == 0) {
            return '未审核';
        }
        return date('Y-m-d H:i:s', $auditTime);
    }

    /**
     * 获取资质类型描述
     */
    public function getQualificationTypeTextAttr($value, $data)
    {
        $goodsId = isset($data['goods_id']) ? intval($data['goods_id']) : 0;
        return $goodsId > 0 ? '商品专用资质' : '通用资质';
    }

    /**
     * 根据商品ID和资质ID查找资质记录（优先商品专用，其次通用）
     */
    public static function findByGoodsAndQualification($shopId, $goodsId, $qualificationId)
    {
        // 优先查找商品专用资质
        $specific = self::where([
            'shop_id' => $shopId,
            'goods_id' => $goodsId,
            'qualification_id' => $qualificationId,
            'del' => 0
        ])->find();

        if ($specific) {
            return $specific;
        }

        // 如果没有商品专用资质，查找通用资质
        return self::where([
            'shop_id' => $shopId,
            'goods_id' => 0,
            'qualification_id' => $qualificationId,
            'del' => 0
        ])->find();
    }

    /**
     * 获取商家的商品专用资质列表
     */
    public static function getGoodsSpecificQualifications($shopId, $goodsId)
    {
        return self::where([
            'shop_id' => $shopId,
            'goods_id' => $goodsId,
            'del' => 0
        ])->with(['qualification'])->select();
    }

    /**
     * 获取商家的通用资质列表
     */
    public static function getGeneralQualifications($shopId)
    {
        return self::where([
            'shop_id' => $shopId,
            'goods_id' => 0,
            'del' => 0
        ])->with(['qualification'])->select();
    }
}
